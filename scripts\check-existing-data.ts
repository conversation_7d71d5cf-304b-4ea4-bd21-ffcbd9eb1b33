/**
 * Check Existing Data Script
 * 
 * This script checks what data exists in the database to understand
 * what foreign key references are available for the test data script.
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🔍 Checking existing data in database...\n');

  try {
    // Check users
    const users = await prisma.user.findMany({
      select: { id: true, name: true, email: true },
      take: 10
    });
    console.log(`👥 Users (${users.length} found, showing first 10):`);
    users.forEach(user => {
      console.log(`   ${user.id} - ${user.name} (${user.email})`);
    });
    console.log();

    // Check services
    const services = await prisma.service.findMany({
      select: { id: true, name: true, price: true },
      take: 10
    });
    console.log(`🛠️  Services (${services.length} found, showing first 10):`);
    services.forEach(service => {
      console.log(`   ${service.id} - ${service.name} (€${service.price / 100})`);
    });
    console.log();

    // Check packages
    const packages = await prisma.packages.findMany({
      select: { id: true, name: true, amount: true },
      take: 10
    });
    console.log(`📦 Packages (${packages.length} found, showing first 10):`);
    packages.forEach(pkg => {
      console.log(`   ${pkg.id} - ${pkg.name} (€${pkg.amount / 100})`);
    });
    console.log();

    // Check immigration services
    const immigrationServices = await prisma.immigration_service.findMany({
      select: { id: true, name: true, amount: true },
      take: 10
    });
    console.log(`🛂 Immigration Services (${immigrationServices.length} found, showing first 10):`);
    immigrationServices.forEach(service => {
      console.log(`   ${service.id} - ${service.name} (€${service.amount / 100})`);
    });
    console.log();

    // Check training
    const training = await prisma.training.findMany({
      select: { id: true, name: true, amount: true },
      take: 10
    });
    console.log(`🎓 Training (${training.length} found, showing first 10):`);
    training.forEach(t => {
      console.log(`   ${t.id} - ${t.name} (€${t.amount / 100})`);
    });
    console.log();

    // Check existing payment data
    const paymentCounts = {
      userMentorService: await prisma.user_mentor_service.count(),
      guestMentorService: await prisma.guest_mentor_service.count(),
      userPackage: await prisma.user_package.count(),
      guestPackage: await prisma.guest_package.count(),
      userImmigrationService: await prisma.user_immigration_service.count(),
      guestImmigrationService: await prisma.guest_immigration_service.count(),
      userTraining: await prisma.user_training.count(),
      guestTraining: await prisma.guest_training.count(),
    };

    console.log('💳 Existing Payment Data:');
    Object.entries(paymentCounts).forEach(([table, count]) => {
      console.log(`   ${table}: ${count} records`);
    });

    const totalPayments = Object.values(paymentCounts).reduce((sum, count) => sum + count, 0);
    console.log(`   Total: ${totalPayments} payment records\n`);

    // Summary for script modification
    console.log('📋 Summary for Test Data Script:');
    console.log(`   Available Users: ${users.length}`);
    console.log(`   Available Services: ${services.length}`);
    console.log(`   Available Packages: ${packages.length}`);
    console.log(`   Available Immigration Services: ${immigrationServices.length}`);
    console.log(`   Available Training: ${training.length}`);

    if (users.length === 0) {
      console.log('\n⚠️  No users found! You need to create some users first.');
    }
    if (services.length === 0) {
      console.log('\n⚠️  No services found! You need to create some services first.');
    }
    if (packages.length === 0) {
      console.log('\n⚠️  No packages found! You need to create some packages first.');
    }
    if (immigrationServices.length === 0) {
      console.log('\n⚠️  No immigration services found! You need to create some immigration services first.');
    }
    if (training.length === 0) {
      console.log('\n⚠️  No training found! You need to create some training first.');
    }

  } catch (error) {
    console.error('❌ Error checking existing data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main().catch(console.error);
