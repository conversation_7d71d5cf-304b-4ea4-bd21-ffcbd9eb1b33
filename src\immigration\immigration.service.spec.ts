/**
 * Immigration Service Unit Tests
 * 
 * Comprehensive unit tests for the ImmigrationService class.
 * Tests cover CRUD operations for immigration services, validation,
 * error handling, and data integrity.
 * 
 * Test Categories:
 * - Service creation and validation
 * - Service updates and modifications
 * - Service deletion and cleanup
 * - Service retrieval and filtering
 * - Error handling and edge cases
 * - Data validation and constraints
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ImmigrationService } from './immigration.service';
import { PrismaService } from '../utils/prisma.service';
import { ImmigrationDto } from './dto/immigration.dto';

describe('ImmigrationService', () => {
  let service: ImmigrationService;
  let prismaService: PrismaService;

  // Mock data
  const mockImmigrationService = {
    id: 'immigration-service-id',
    name: 'Visa Consultation',
    amount: 15000, // €150.00 in cents
    order: 1,
    service: ['Document Review', 'Application Assistance', 'Interview Preparation'],
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ImmigrationService,
        {
          provide: PrismaService,
          useValue: {
            immigration_service: {
              create: jest.fn(),
              update: jest.fn(),
              delete: jest.fn(),
              findMany: jest.fn(),
              findUnique: jest.fn(),
            },
          },
        },
      ],
    }).compile();

    service = module.get<ImmigrationService>(ImmigrationService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    const createDto: ImmigrationDto = {
      name: 'Visa Consultation',
      amount: 15000,
      order: 1,
      service: ['Document Review', 'Application Assistance', 'Interview Preparation'],
    };

    it('should create immigration service successfully', async () => {
      // Arrange
      prismaService.immigration_service.create = jest.fn().mockResolvedValue(mockImmigrationService);

      // Act
      const result = await service.create(createDto);

      // Assert
      expect(result).toEqual(mockImmigrationService);
      expect(prismaService.immigration_service.create).toHaveBeenCalledWith({
        data: createDto,
      });
    });

    it('should handle creation with minimal data', async () => {
      // Arrange
      const minimalDto: ImmigrationDto = {
        name: 'Basic Service',
        amount: 5000,
        service: ['Basic Consultation'],
      };
      const minimalService = {
        ...mockImmigrationService,
        ...minimalDto,
        order: null,
      };
      prismaService.immigration_service.create = jest.fn().mockResolvedValue(minimalService);

      // Act
      const result = await service.create(minimalDto);

      // Assert
      expect(result).toEqual(minimalService);
      expect(prismaService.immigration_service.create).toHaveBeenCalledWith({
        data: minimalDto,
      });
    });

    it('should handle creation errors', async () => {
      // Arrange
      prismaService.immigration_service.create = jest.fn().mockRejectedValue(
        new Error('Database connection failed'),
      );

      // Act & Assert
      await expect(service.create(createDto)).rejects.toThrow(
        'Database connection failed',
      );
    });

    it('should validate service array is not empty', () => {
      // Arrange
      const invalidDto = {
        name: 'Invalid Service',
        amount: 1000,
        service: [], // Empty array should be invalid
      };

      // Assert - This would be caught by class-validator in real scenario
      expect(invalidDto.service.length).toBe(0);
    });

    it('should validate amount is positive', () => {
      // Arrange
      const invalidDto = {
        name: 'Invalid Service',
        amount: -1000, // Negative amount should be invalid
        service: ['Some Service'],
      };

      // Assert - This would be caught by class-validator in real scenario
      expect(invalidDto.amount).toBeLessThan(0);
    });
  });

  describe('update', () => {
    const updateDto: ImmigrationDto = {
      name: 'Updated Visa Consultation',
      amount: 20000,
      order: 2,
      service: ['Enhanced Document Review', 'Priority Application Assistance'],
    };

    it('should update immigration service successfully', async () => {
      // Arrange
      const updatedService = { ...mockImmigrationService, ...updateDto };
      prismaService.immigration_service.update = jest.fn().mockResolvedValue(updatedService);

      // Act
      const result = await service.update('immigration-service-id', updateDto);

      // Assert
      expect(result).toEqual(updatedService);
      expect(prismaService.immigration_service.update).toHaveBeenCalledWith({
        where: { id: 'immigration-service-id' },
        data: updateDto,
      });
    });

    it('should handle partial updates', async () => {
      // Arrange
      const partialDto = { name: 'Partially Updated Service' };
      const partiallyUpdated = { ...mockImmigrationService, ...partialDto };
      prismaService.immigration_service.update = jest.fn().mockResolvedValue(partiallyUpdated);

      // Act
      const result = await service.update('immigration-service-id', partialDto as ImmigrationDto);

      // Assert
      expect(result).toEqual(partiallyUpdated);
      expect(prismaService.immigration_service.update).toHaveBeenCalledWith({
        where: { id: 'immigration-service-id' },
        data: partialDto,
      });
    });

    it('should handle update of non-existent service', async () => {
      // Arrange
      prismaService.immigration_service.update = jest.fn().mockRejectedValue(
        new Error('Record to update not found'),
      );

      // Act & Assert
      await expect(service.update('non-existent-id', updateDto)).rejects.toThrow(
        'Record to update not found',
      );
    });

    it('should handle database errors during update', async () => {
      // Arrange
      prismaService.immigration_service.update = jest.fn().mockRejectedValue(
        new Error('Database constraint violation'),
      );

      // Act & Assert
      await expect(service.update('immigration-service-id', updateDto)).rejects.toThrow(
        'Database constraint violation',
      );
    });
  });

  describe('remove', () => {
    it('should delete immigration service successfully', async () => {
      // Arrange
      prismaService.immigration_service.delete = jest.fn().mockResolvedValue(mockImmigrationService);

      // Act
      const result = await service.remove('immigration-service-id');

      // Assert
      expect(result).toEqual(mockImmigrationService);
      expect(prismaService.immigration_service.delete).toHaveBeenCalledWith({
        where: { id: 'immigration-service-id' },
      });
    });

    it('should handle deletion of non-existent service', async () => {
      // Arrange
      prismaService.immigration_service.delete = jest.fn().mockRejectedValue(
        new Error('Record to delete does not exist'),
      );

      // Act & Assert
      await expect(service.remove('non-existent-id')).rejects.toThrow(
        'Record to delete does not exist',
      );
    });

    it('should handle foreign key constraint errors', async () => {
      // Arrange
      prismaService.immigration_service.delete = jest.fn().mockRejectedValue(
        new Error('Foreign key constraint failed'),
      );

      // Act & Assert
      await expect(service.remove('immigration-service-id')).rejects.toThrow(
        'Foreign key constraint failed',
      );
    });
  });

  describe('getAll', () => {
    const mockServices = [
      mockImmigrationService,
      {
        ...mockImmigrationService,
        id: 'immigration-service-2',
        name: 'Work Permit Assistance',
        order: 2,
      },
      {
        ...mockImmigrationService,
        id: 'immigration-service-3',
        name: 'Citizenship Application',
        order: 3,
      },
    ];

    it('should retrieve all immigration services ordered correctly', async () => {
      // Arrange
      prismaService.immigration_service.findMany = jest.fn().mockResolvedValue(mockServices);

      // Act
      const result = await service.getAll();

      // Assert
      expect(result).toEqual(mockServices);
      expect(prismaService.immigration_service.findMany).toHaveBeenCalledWith({
        orderBy: [{ order: 'asc' }, { createdAt: 'desc' }],
      });
    });

    it('should handle empty result set', async () => {
      // Arrange
      prismaService.immigration_service.findMany = jest.fn().mockResolvedValue([]);

      // Act
      const result = await service.getAll();

      // Assert
      expect(result).toEqual([]);
      expect(result.length).toBe(0);
    });

    it('should handle database errors during retrieval', async () => {
      // Arrange
      prismaService.immigration_service.findMany = jest.fn().mockRejectedValue(
        new Error('Database connection timeout'),
      );

      // Act & Assert
      await expect(service.getAll()).rejects.toThrow(
        'Database connection timeout',
      );
    });

    it('should maintain correct ordering', async () => {
      // Arrange
      const unorderedServices = [
        { ...mockImmigrationService, order: 3, createdAt: new Date('2024-01-01') },
        { ...mockImmigrationService, order: 1, createdAt: new Date('2024-01-03') },
        { ...mockImmigrationService, order: 2, createdAt: new Date('2024-01-02') },
      ];
      prismaService.immigration_service.findMany = jest.fn().mockResolvedValue(unorderedServices);

      // Act
      const result = await service.getAll();

      // Assert
      expect(prismaService.immigration_service.findMany).toHaveBeenCalledWith({
        orderBy: [{ order: 'asc' }, { createdAt: 'desc' }],
      });
      // In real scenario, Prisma would handle the ordering
      expect(result).toEqual(unorderedServices);
    });
  });

  describe('edge cases and validation', () => {
    it('should handle very large amounts', async () => {
      // Arrange
      const largeAmountDto: ImmigrationDto = {
        name: 'Premium Service',
        amount: 999999999, // Very large amount
        service: ['Premium Consultation'],
      };
      const largeAmountService = { ...mockImmigrationService, ...largeAmountDto };
      prismaService.immigration_service.create = jest.fn().mockResolvedValue(largeAmountService);

      // Act
      const result = await service.create(largeAmountDto);

      // Assert
      expect(result.amount).toBe(999999999);
    });

    it('should handle services with many items', async () => {
      // Arrange
      const manyServicesDto: ImmigrationDto = {
        name: 'Comprehensive Package',
        amount: 50000,
        service: Array.from({ length: 20 }, (_, i) => `Service ${i + 1}`),
      };
      const manyServicesService = { ...mockImmigrationService, ...manyServicesDto };
      prismaService.immigration_service.create = jest.fn().mockResolvedValue(manyServicesService);

      // Act
      const result = await service.create(manyServicesDto);

      // Assert
      expect(result.service).toHaveLength(20);
    });

    it('should handle special characters in service names', async () => {
      // Arrange
      const specialCharDto: ImmigrationDto = {
        name: 'Service with Special Chars: €, ñ, 中文',
        amount: 10000,
        service: ['Service with émojis 🇮🇪', 'Ñoño service'],
      };
      const specialCharService = { ...mockImmigrationService, ...specialCharDto };
      prismaService.immigration_service.create = jest.fn().mockResolvedValue(specialCharService);

      // Act
      const result = await service.create(specialCharDto);

      // Assert
      expect(result.name).toContain('€');
      expect(result.service[0]).toContain('🇮🇪');
    });
  });
});
