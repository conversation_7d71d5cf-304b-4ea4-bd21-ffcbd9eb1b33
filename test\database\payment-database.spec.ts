/**
 * Payment Database Tests
 * 
 * Comprehensive database-specific tests for the unified payment system.
 * Tests database schema, constraints, performance, and data integrity.
 * 
 * Test Categories:
 * - Schema validation and constraints
 * - Foreign key relationships
 * - Index performance testing
 * - Data migration validation
 * - Concurrent access testing
 * - Query optimization verification
 * - Data integrity checks
 */

import { PrismaClient } from '@prisma/client';
import { PaymentFixtures } from '../fixtures/payment-fixtures';
import { DatabaseTestUtils } from '../utils/database-test-utils';
import { ServiceType, PaymentType } from '../../src/payment/dto/payment.dto';

describe('Payment Database Tests', () => {
  let prisma: PrismaClient;

  beforeAll(async () => {
    prisma = await DatabaseTestUtils.initialize();
  });

  beforeEach(async () => {
    await DatabaseTestUtils.clearPaymentData();
    await DatabaseTestUtils.seedPaymentTestData();
  });

  afterAll(async () => {
    await DatabaseTestUtils.cleanup();
  });

  describe('Schema Validation and Constraints', () => {
    /**
     * Test: Foreign Key Constraints
     * Purpose: Verify foreign key relationships are properly enforced
     * Expected: Database rejects invalid foreign key references
     */
    it('should enforce foreign key constraints', async () => {
      // Test invalid service reference
      await expect(
        prisma.payment.create({
          data: {
            ...PaymentFixtures.getMockUserPayment(),
            id: 'test-fk-service',
            serviceId: 'non-existent-service-id',
          },
        })
      ).rejects.toThrow();

      // Test invalid user reference
      await expect(
        prisma.payment.create({
          data: {
            ...PaymentFixtures.getMockUserPayment(),
            id: 'test-fk-user',
            userId: 'non-existent-user-id',
          },
        })
      ).rejects.toThrow();
    });

    /**
     * Test: Unique Constraints
     * Purpose: Verify unique constraints prevent duplicate data
     * Expected: Database rejects duplicate unique values
     */
    it('should enforce unique constraints', async () => {
      // Create first payment with unique session ID
      await prisma.payment.create({
        data: {
          ...PaymentFixtures.getMockUserPayment(),
          id: 'test-unique-1',
          stripeSessionId: 'unique-session-123',
        },
      });

      // Attempt to create second payment with same session ID
      await expect(
        prisma.payment.create({
          data: {
            ...PaymentFixtures.getMockUserPayment(),
            id: 'test-unique-2',
            stripeSessionId: 'unique-session-123', // Duplicate
          },
        })
      ).rejects.toThrow();
    });

    /**
     * Test: Check Constraints
     * Purpose: Verify check constraints validate data values
     * Expected: Database rejects invalid data values
     */
    it('should enforce check constraints', async () => {
      // Test negative amount (should fail)
      await expect(
        prisma.payment.create({
          data: {
            ...PaymentFixtures.getMockUserPayment(),
            id: 'test-check-amount',
            amount: -1000,
          },
        })
      ).rejects.toThrow();

      // Test zero amount (should fail)
      await expect(
        prisma.payment.create({
          data: {
            ...PaymentFixtures.getMockUserPayment(),
            id: 'test-check-zero',
            amount: 0,
          },
        })
      ).rejects.toThrow();
    });

    /**
     * Test: Enum Constraints
     * Purpose: Verify enum fields only accept valid values
     * Expected: Database rejects invalid enum values
     */
    it('should enforce enum constraints', async () => {
      // Test invalid service type
      await expect(
        prisma.payment.create({
          data: {
            ...PaymentFixtures.getMockUserPayment(),
            id: 'test-enum-service',
            serviceType: 'invalid-service-type' as any,
          },
        })
      ).rejects.toThrow();

      // Test invalid payment type
      await expect(
        prisma.payment.create({
          data: {
            ...PaymentFixtures.getMockUserPayment(),
            id: 'test-enum-payment',
            paymentType: 'invalid-payment-type' as any,
          },
        })
      ).rejects.toThrow();
    });
  });

  describe('Index Performance Testing', () => {
    /**
     * Test: Query Performance with Indexes
     * Purpose: Verify database indexes improve query performance
     * Expected: Indexed queries perform within acceptable thresholds
     */
    it('should perform efficiently with proper indexes', async () => {
      // Generate test data
      await DatabaseTestUtils.generateLoadTestData(1000);

      // Test indexed queries
      const queries = [
        // Query by user ID (should use user_id index)
        () => prisma.payment.findMany({
          where: { userId: PaymentFixtures.TEST_USER_ID },
        }),
        
        // Query by service type (should use service_type index)
        () => prisma.payment.findMany({
          where: { serviceType: ServiceType.SERVICE },
        }),
        
        // Query by status (should use status index)
        () => prisma.payment.findMany({
          where: { status: 'completed' },
        }),
        
        // Query by date range (should use created_at index)
        () => prisma.payment.findMany({
          where: {
            createdAt: {
              gte: new Date('2024-01-01'),
              lte: new Date('2024-12-31'),
            },
          },
        }),
      ];

      for (const query of queries) {
        const performance = await DatabaseTestUtils.measureQueryPerformance(
          query,
          5 // Run 5 times for average
        );
        
        // Assert performance threshold (100ms average)
        expect(performance.averageTime).toBeLessThan(100);
      }

      // Cleanup
      await DatabaseTestUtils.cleanupLoadTestData();
    });

    /**
     * Test: Complex Query Performance
     * Purpose: Verify complex analytical queries perform well
     * Expected: Complex queries complete within reasonable time
     */
    it('should handle complex analytical queries efficiently', async () => {
      // Generate test data
      await DatabaseTestUtils.generateLoadTestData(500);

      // Complex aggregation query
      const complexQuery = () => prisma.payment.groupBy({
        by: ['serviceType', 'paymentType', 'status'],
        _sum: { amount: true },
        _count: { id: true },
        _avg: { amount: true },
        where: {
          createdAt: {
            gte: new Date('2024-01-01'),
          },
        },
      });

      const performance = await DatabaseTestUtils.measureQueryPerformance(
        complexQuery,
        3
      );

      // Assert performance threshold (500ms for complex query)
      expect(performance.averageTime).toBeLessThan(500);

      // Cleanup
      await DatabaseTestUtils.cleanupLoadTestData();
    });
  });

  describe('Data Migration Validation', () => {
    /**
     * Test: Legacy Data Compatibility
     * Purpose: Verify unified table can handle legacy data structures
     * Expected: Legacy data formats are properly handled
     */
    it('should handle legacy data migration correctly', async () => {
      // Simulate legacy payment data
      const legacyPayments = [
        {
          id: 'legacy-mentor-payment',
          serviceType: ServiceType.SERVICE,
          serviceId: PaymentFixtures.TEST_SERVICE_ID,
          paymentType: PaymentType.USER,
          userId: PaymentFixtures.TEST_USER_ID,
          amount: 15000,
          currency: 'eur',
          status: 'completed',
          // Legacy fields that might be null
          stripeSessionId: null,
          stripePaymentIntentId: 'pi_legacy_123',
          completedAt: new Date('2024-01-01'),
        },
        {
          id: 'legacy-guest-payment',
          serviceType: ServiceType.PACKAGE,
          serviceId: PaymentFixtures.TEST_PACKAGE_ID,
          paymentType: PaymentType.GUEST,
          userId: null,
          amount: 25000,
          currency: 'eur',
          status: 'completed',
          guestName: 'Legacy Guest',
          guestEmail: '<EMAIL>',
          guestMobile: '+353123456789',
          stripeSessionId: 'cs_legacy_123',
          stripePaymentIntentId: null,
          completedAt: new Date('2024-01-01'),
        },
      ];

      // Insert legacy data
      for (const payment of legacyPayments) {
        const created = await prisma.payment.create({ data: payment });
        expect(created.id).toBe(payment.id);
      }

      // Verify data integrity
      const integrity = await DatabaseTestUtils.verifyDataIntegrity();
      expect(integrity.orphanedPayments).toBe(0);
      expect(integrity.invalidAmounts).toBe(0);
      expect(integrity.missingRequiredFields).toBe(0);
    });
  });

  describe('Concurrent Access Testing', () => {
    /**
     * Test: Concurrent Payment Creation
     * Purpose: Verify database handles concurrent payment creation safely
     * Expected: No data corruption or constraint violations
     */
    it('should handle concurrent payment creation safely', async () => {
      const concurrentPayments = Array.from({ length: 10 }, (_, index) => ({
        ...PaymentFixtures.getMockUserPayment(),
        id: `concurrent-payment-${index}`,
        stripeSessionId: `cs_concurrent_${index}`,
      }));

      // Create payments concurrently
      const promises = concurrentPayments.map(payment =>
        prisma.payment.create({ data: payment })
      );

      const results = await Promise.all(promises);

      // Verify all payments were created
      expect(results).toHaveLength(10);
      expect(results.every(result => result.id.startsWith('concurrent-payment-'))).toBe(true);

      // Verify no duplicate session IDs
      const sessionIds = results.map(r => r.stripeSessionId);
      const uniqueSessionIds = new Set(sessionIds);
      expect(uniqueSessionIds.size).toBe(sessionIds.length);
    });

    /**
     * Test: Concurrent Payment Updates
     * Purpose: Verify database handles concurrent updates correctly
     * Expected: Updates are applied atomically without conflicts
     */
    it('should handle concurrent payment updates safely', async () => {
      // Create initial payment
      const payment = await prisma.payment.create({
        data: {
          ...PaymentFixtures.getMockUserPayment(),
          id: 'concurrent-update-test',
        },
      });

      // Concurrent updates
      const updatePromises = [
        prisma.payment.update({
          where: { id: payment.id },
          data: { status: 'completed' },
        }),
        prisma.payment.update({
          where: { id: payment.id },
          data: { stripePaymentIntentId: 'pi_updated_123' },
        }),
        prisma.payment.update({
          where: { id: payment.id },
          data: { completedAt: new Date() },
        }),
      ];

      // All updates should succeed
      const results = await Promise.all(updatePromises);
      expect(results).toHaveLength(3);

      // Verify final state
      const finalPayment = await prisma.payment.findUnique({
        where: { id: payment.id },
      });
      expect(finalPayment.status).toBe('completed');
      expect(finalPayment.stripePaymentIntentId).toBe('pi_updated_123');
      expect(finalPayment.completedAt).toBeDefined();
    });
  });

  describe('Query Optimization Verification', () => {
    /**
     * Test: Efficient Filtering Queries
     * Purpose: Verify filtering queries use appropriate indexes
     * Expected: Queries execute efficiently with proper index usage
     */
    it('should execute filtering queries efficiently', async () => {
      // Generate test data
      await DatabaseTestUtils.generateLoadTestData(1000);

      // Test various filtering scenarios
      const filterTests = [
        {
          name: 'Filter by service type and status',
          query: () => prisma.payment.findMany({
            where: {
              serviceType: ServiceType.SERVICE,
              status: 'completed',
            },
          }),
        },
        {
          name: 'Filter by payment type and date range',
          query: () => prisma.payment.findMany({
            where: {
              paymentType: PaymentType.USER,
              createdAt: {
                gte: new Date('2024-01-01'),
                lte: new Date('2024-12-31'),
              },
            },
          }),
        },
        {
          name: 'Filter by user and service type',
          query: () => prisma.payment.findMany({
            where: {
              userId: PaymentFixtures.TEST_USER_ID,
              serviceType: ServiceType.PACKAGE,
            },
          }),
        },
      ];

      for (const test of filterTests) {
        const performance = await DatabaseTestUtils.measureQueryPerformance(
          test.query,
          3
        );
        
        // Assert performance (should be under 50ms for filtered queries)
        expect(performance.averageTime).toBeLessThan(50);
      }

      // Cleanup
      await DatabaseTestUtils.cleanupLoadTestData();
    });

    /**
     * Test: Pagination Performance
     * Purpose: Verify pagination queries perform well with large datasets
     * Expected: Pagination maintains consistent performance
     */
    it('should handle pagination efficiently', async () => {
      // Generate test data
      await DatabaseTestUtils.generateLoadTestData(2000);

      const pageSize = 50;
      const pages = [0, 10, 20, 30]; // Test different page offsets

      for (const page of pages) {
        const paginationQuery = () => prisma.payment.findMany({
          skip: page * pageSize,
          take: pageSize,
          orderBy: { createdAt: 'desc' },
        });

        const performance = await DatabaseTestUtils.measureQueryPerformance(
          paginationQuery,
          3
        );

        // Pagination should maintain consistent performance
        expect(performance.averageTime).toBeLessThan(100);
      }

      // Cleanup
      await DatabaseTestUtils.cleanupLoadTestData();
    });
  });

  describe('Data Integrity Checks', () => {
    /**
     * Test: Referential Integrity
     * Purpose: Verify all payment references are valid
     * Expected: No orphaned or invalid references
     */
    it('should maintain referential integrity', async () => {
      // Create various payment types
      const payments = [
        {
          ...PaymentFixtures.getMockUserPayment(),
          id: 'integrity-user-service',
        },
        {
          ...PaymentFixtures.getMockGuestPayment(),
          id: 'integrity-guest-package',
        },
      ];

      for (const payment of payments) {
        await prisma.payment.create({ data: payment });
      }

      // Verify integrity
      const integrity = await DatabaseTestUtils.verifyDataIntegrity();
      expect(integrity.orphanedPayments).toBe(0);
      expect(integrity.invalidAmounts).toBe(0);
      expect(integrity.missingRequiredFields).toBe(0);
    });

    /**
     * Test: Data Consistency After Operations
     * Purpose: Verify data remains consistent after various operations
     * Expected: All data relationships remain valid
     */
    it('should maintain consistency after bulk operations', async () => {
      // Perform bulk operations
      const bulkPayments = Array.from({ length: 100 }, (_, index) => ({
        ...PaymentFixtures.getMockUserPayment(),
        id: `bulk-payment-${index}`,
        stripeSessionId: `cs_bulk_${index}`,
        amount: (index + 1) * 1000,
      }));

      await prisma.payment.createMany({
        data: bulkPayments,
        skipDuplicates: true,
      });

      // Update some payments
      await prisma.payment.updateMany({
        where: {
          id: { in: bulkPayments.slice(0, 50).map(p => p.id) },
        },
        data: { status: 'completed' },
      });

      // Delete some payments
      await prisma.payment.deleteMany({
        where: {
          id: { in: bulkPayments.slice(75).map(p => p.id) },
        },
      });

      // Verify consistency
      const integrity = await DatabaseTestUtils.verifyDataIntegrity();
      expect(integrity.orphanedPayments).toBe(0);
      expect(integrity.invalidAmounts).toBe(0);
      expect(integrity.missingRequiredFields).toBe(0);

      // Verify expected counts
      const remainingCount = await prisma.payment.count({
        where: {
          id: { startsWith: 'bulk-payment-' },
        },
      });
      expect(remainingCount).toBe(75); // 100 - 25 deleted
    });
  });
});
