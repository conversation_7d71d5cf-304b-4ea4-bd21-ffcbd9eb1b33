model payment {
  id                       String               @id @default(cuid())
  amount                   Int
  status                   String
  payment_type             String
  service_type             String
  progress                 Status               @default(Pending)
  userId                   String?
  serviceId                String?
  packageId                String?
  immigration_serviceId    String?
  trainingId               String?
  guest_name               String?
  guest_email              String?
  guest_mobile             String?
  stripe_session_id        String?
  stripe_payment_intent_id String?
  createdAt                DateTime             @default(now())
  updatedAt                DateTime             @updatedAt
  immigration_service      immigration_service? @relation(fields: [immigration_serviceId], references: [id])
  package                  packages?            @relation(fields: [packageId], references: [id])
  service                  service?             @relation(fields: [serviceId], references: [id])
  training                 training?            @relation(fields: [trainingId], references: [id])
  user                     user?                @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([status])
  @@index([createdAt])
  @@index([payment_type])
  @@index([service_type])
  @@index([stripe_payment_intent_id])
  @@index([stripe_session_id])
  @@map("payment")
}
