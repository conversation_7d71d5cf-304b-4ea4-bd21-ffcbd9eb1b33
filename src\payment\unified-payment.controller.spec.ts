/**
 * Unified Payment Controller Unit Tests
 * 
 * Comprehensive unit tests for the UnifiedPaymentController class.
 * Tests cover all HTTP endpoints, request validation, response formatting,
 * authentication, and error handling.
 * 
 * Test Categories:
 * - Payment creation endpoints
 * - Webhook handling
 * - Payment history retrieval
 * - Analytics endpoints
 * - Authentication and authorization
 * - Input validation
 * - Error responses
 */

import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import { UnifiedPaymentController } from './unified-payment.controller';
import { UnifiedPaymentService } from './unified-payment.service';
import { CreateUnifiedPaymentDto, ServiceType, PaymentType } from './dto/payment.dto';

describe('UnifiedPaymentController', () => {
  let controller: UnifiedPaymentController;
  let service: UnifiedPaymentService;

  // Mock data
  const mockUser = global.testUtils.createJWTPayload();
  const mockPaymentResponse = {
    status: 'success',
    url: 'https://checkout.stripe.com/test',
    paymentId: 'test-payment-id',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UnifiedPaymentController],
      providers: [
        {
          provide: UnifiedPaymentService,
          useValue: {
            createPayment: jest.fn(),
            processWebhook: jest.fn(),
            getPaymentHistory: jest.fn(),
            getPaymentAnalytics: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<UnifiedPaymentController>(UnifiedPaymentController);
    service = module.get<UnifiedPaymentService>(UnifiedPaymentService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createPayment', () => {
    const validDto: CreateUnifiedPaymentDto = {
      serviceType: ServiceType.SERVICE,
      serviceId: 'test-service-id',
      paymentType: PaymentType.USER,
    };

    it('should create user payment successfully', async () => {
      // Arrange
      service.createPayment = jest.fn().mockResolvedValue(mockPaymentResponse);

      // Act
      const result = await controller.createPayment(mockUser, validDto);

      // Assert
      expect(result).toEqual(mockPaymentResponse);
      expect(service.createPayment).toHaveBeenCalledWith(mockUser, validDto);
    });

    it('should create guest payment with guest info', async () => {
      // Arrange
      const guestDto: CreateUnifiedPaymentDto = {
        ...validDto,
        paymentType: PaymentType.GUEST,
        name: 'Guest User',
        email: '<EMAIL>',
        mobile: '+353123456789',
      };
      service.createPayment = jest.fn().mockResolvedValue(mockPaymentResponse);

      // Act
      const result = await controller.createPayment(null, guestDto);

      // Assert
      expect(result).toEqual(mockPaymentResponse);
      expect(service.createPayment).toHaveBeenCalledWith(null, guestDto);
    });

    it('should throw error for user payment without authentication', async () => {
      // Arrange
      const userDto = { ...validDto, paymentType: PaymentType.USER };

      // Act & Assert
      await expect(controller.createPayment(null, userDto)).rejects.toThrow(
        BadRequestException,
      );
      expect(service.createPayment).not.toHaveBeenCalled();
    });

    it('should handle service errors', async () => {
      // Arrange
      service.createPayment = jest.fn().mockRejectedValue(
        new BadRequestException('Service not found'),
      );

      // Act & Assert
      await expect(controller.createPayment(mockUser, validDto)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should validate DTO properties', async () => {
      // Test with invalid service type
      const invalidDto = {
        ...validDto,
        serviceType: 'invalid-type' as ServiceType,
      };

      // This would be caught by class-validator in real scenario
      // Here we simulate the validation
      expect(Object.values(ServiceType)).not.toContain(invalidDto.serviceType);
    });
  });

  describe('createGuestPayment', () => {
    const guestDto: CreateUnifiedPaymentDto = {
      serviceType: ServiceType.PACKAGE,
      serviceId: 'test-package-id',
      paymentType: PaymentType.USER, // Will be overridden to GUEST
      name: 'Guest User',
      email: '<EMAIL>',
      mobile: '+353123456789',
    };

    it('should create guest payment and override payment type', async () => {
      // Arrange
      service.createPayment = jest.fn().mockResolvedValue(mockPaymentResponse);

      // Act
      const result = await controller.createGuestPayment(guestDto);

      // Assert
      expect(result).toEqual(mockPaymentResponse);
      expect(service.createPayment).toHaveBeenCalledWith(null, {
        ...guestDto,
        paymentType: PaymentType.GUEST,
      });
    });

    it('should handle missing guest information', async () => {
      // Arrange
      const incompleteDto = {
        serviceType: ServiceType.SERVICE,
        serviceId: 'test-service-id',
        paymentType: PaymentType.GUEST,
        // Missing name, email, mobile
      };
      service.createPayment = jest.fn().mockRejectedValue(
        new BadRequestException('Guest information required'),
      );

      // Act & Assert
      await expect(controller.createGuestPayment(incompleteDto)).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('handleWebhook', () => {
    const mockRequest = {
      body: Buffer.from('test-webhook-body'),
      headers: {
        'stripe-signature': 'test-signature',
      },
    } as any;

    it('should process webhook successfully', async () => {
      // Arrange
      service.processWebhook = jest.fn().mockResolvedValue({ received: true });

      // Act
      const result = await controller.handleWebhook(mockRequest);

      // Assert
      expect(result).toEqual({ received: true });
      expect(service.processWebhook).toHaveBeenCalledWith(mockRequest);
    });

    it('should handle webhook processing errors', async () => {
      // Arrange
      service.processWebhook = jest.fn().mockRejectedValue(
        new BadRequestException('Invalid webhook signature'),
      );

      // Act & Assert
      await expect(controller.handleWebhook(mockRequest)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should handle malformed webhook requests', async () => {
      // Arrange
      const malformedRequest = {
        body: null,
        headers: {},
      } as any;
      service.processWebhook = jest.fn().mockRejectedValue(
        new BadRequestException('Invalid request format'),
      );

      // Act & Assert
      await expect(controller.handleWebhook(malformedRequest)).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('getPaymentHistory', () => {
    const mockPayments = [
      global.testUtils.createTestPayment(),
      global.testUtils.createTestPayment({ id: 'payment-2' }),
    ];

    it('should return payment history with filters', async () => {
      // Arrange
      const filters = {
        serviceType: ServiceType.SERVICE,
        paymentType: PaymentType.USER,
        status: 'completed',
      };
      service.getPaymentHistory = jest.fn().mockResolvedValue(mockPayments);

      // Act
      const result = await controller.getPaymentHistory(filters);

      // Assert
      expect(result).toEqual(mockPayments);
      expect(service.getPaymentHistory).toHaveBeenCalledWith(filters);
    });

    it('should return all payments when no filters provided', async () => {
      // Arrange
      service.getPaymentHistory = jest.fn().mockResolvedValue(mockPayments);

      // Act
      const result = await controller.getPaymentHistory({});

      // Assert
      expect(result).toEqual(mockPayments);
      expect(service.getPaymentHistory).toHaveBeenCalledWith({});
    });

    it('should handle date range filters', async () => {
      // Arrange
      const filters = {
        startDate: '2024-01-01',
        endDate: '2024-12-31',
      };
      service.getPaymentHistory = jest.fn().mockResolvedValue(mockPayments);

      // Act
      const result = await controller.getPaymentHistory(filters);

      // Assert
      expect(result).toEqual(mockPayments);
      expect(service.getPaymentHistory).toHaveBeenCalledWith(filters);
    });
  });

  describe('getPaymentAnalytics', () => {
    const mockAnalytics = {
      totalRevenue: 100000,
      totalPayments: 50,
      revenueByPaymentType: [
        { paymentType: 'user', _sum: { amount: 80000 }, _count: 40 },
        { paymentType: 'guest', _sum: { amount: 20000 }, _count: 10 },
      ],
      revenueByServiceType: [
        { serviceType: 'service', _sum: { amount: 60000 }, _count: 30 },
        { serviceType: 'package', _sum: { amount: 40000 }, _count: 20 },
      ],
    };

    it('should return payment analytics', async () => {
      // Arrange
      service.getPaymentAnalytics = jest.fn().mockResolvedValue(mockAnalytics);

      // Act
      const result = await controller.getPaymentAnalytics();

      // Assert
      expect(result).toEqual(mockAnalytics);
      expect(service.getPaymentAnalytics).toHaveBeenCalled();
    });

    it('should handle analytics calculation errors', async () => {
      // Arrange
      service.getPaymentAnalytics = jest.fn().mockRejectedValue(
        new Error('Database connection failed'),
      );

      // Act & Assert
      await expect(controller.getPaymentAnalytics()).rejects.toThrow(
        'Database connection failed',
      );
    });
  });

  describe('healthCheck', () => {
    it('should return service health status', async () => {
      // Act
      const result = await controller.healthCheck();

      // Assert
      expect(result).toEqual({
        status: 'OK',
        service: 'unified-payment-service',
        version: '2.0.0',
        timestamp: expect.any(String),
      });
    });

    it('should return current timestamp', async () => {
      // Arrange
      const beforeCall = new Date();

      // Act
      const result = await controller.healthCheck();
      const afterCall = new Date();

      // Assert
      const resultTimestamp = new Date(result.timestamp);
      expect(resultTimestamp.getTime()).toBeGreaterThanOrEqual(beforeCall.getTime());
      expect(resultTimestamp.getTime()).toBeLessThanOrEqual(afterCall.getTime());
    });
  });
});
