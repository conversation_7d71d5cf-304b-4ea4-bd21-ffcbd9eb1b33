/**
 * Payment Integration Tests
 * 
 * Comprehensive integration tests for the unified payment system.
 * Tests the complete payment flow including database operations,
 * external service integrations, and business logic.
 * 
 * Test Categories:
 * - End-to-end payment creation flows
 * - Database transaction integrity
 * - Stripe integration testing
 * - Email notification integration
 * - Error handling and recovery
 * - Performance and load testing
 * - Data consistency validation
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as request from 'supertest';
import { PrismaClient } from '@prisma/client';
import { UnifiedPaymentService } from '../../src/payment/unified-payment.service';
import { UnifiedPaymentController } from '../../src/payment/unified-payment.controller';
import { PrismaService } from '../../src/utils/prisma.service';
import { MailerService } from '../../src/mailer/mailer.service';
import { PaymentFixtures } from '../fixtures/payment-fixtures';
import { DatabaseTestUtils } from '../utils/database-test-utils';
import { ServiceType, PaymentType } from '../../src/payment/dto/payment.dto';

describe('Payment Integration Tests', () => {
  let app: INestApplication;
  let prisma: PrismaClient;
  let paymentService: UnifiedPaymentService;
  let mockStripe: any;
  let mockMailer: any;

  beforeAll(async () => {
    // Initialize test database
    prisma = await DatabaseTestUtils.initialize();
    
    // Setup mock Stripe
    mockStripe = {
      checkout: {
        sessions: {
          create: jest.fn(),
          retrieve: jest.fn(),
        },
      },
      webhooks: {
        constructEvent: jest.fn(),
      },
    };

    // Setup mock mailer
    mockMailer = {
      sendPaymentConfirmation: jest.fn(),
      sendAdminNotification: jest.fn(),
    };

    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [UnifiedPaymentController],
      providers: [
        UnifiedPaymentService,
        {
          provide: PrismaService,
          useValue: prisma,
        },
        {
          provide: MailerService,
          useValue: mockMailer,
        },
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn().mockReturnValue('test-jwt-token'),
            verify: jest.fn().mockReturnValue({ id: PaymentFixtures.TEST_USER_ID }),
          },
        },
        {
          provide: 'STRIPE_CLIENT',
          useValue: mockStripe,
        },
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    paymentService = moduleFixture.get<UnifiedPaymentService>(UnifiedPaymentService);
  });

  beforeEach(async () => {
    // Clear and seed test data before each test
    await DatabaseTestUtils.clearPaymentData();
    await DatabaseTestUtils.seedPaymentTestData();
    
    // Reset mocks
    jest.clearAllMocks();
    
    // Setup default Stripe mock responses
    mockStripe.checkout.sessions.create.mockResolvedValue(
      PaymentFixtures.getMockStripeSession()
    );
  });

  afterAll(async () => {
    await DatabaseTestUtils.cleanup();
    await app.close();
  });

  describe('Complete Payment Flow Integration', () => {
    /**
     * Test: User Service Payment Complete Flow
     * Purpose: Verify end-to-end user payment creation and processing
     * Dependencies: Database, Stripe, Email service
     */
    it('should complete user service payment flow successfully', async () => {
      // Arrange
      const paymentDto = PaymentFixtures.getValidUserServicePayment();
      const userPayload = { id: PaymentFixtures.TEST_USER_ID };

      // Act - Create payment
      const paymentResult = await paymentService.createPayment(userPayload, paymentDto);

      // Assert - Payment creation
      expect(paymentResult.status).toBe('success');
      expect(paymentResult.url).toBeDefined();
      expect(paymentResult.paymentId).toBeDefined();

      // Verify database record
      const dbPayment = await prisma.payment.findUnique({
        where: { id: paymentResult.paymentId },
      });
      expect(dbPayment).toBeDefined();
      expect(dbPayment.serviceType).toBe(ServiceType.SERVICE);
      expect(dbPayment.paymentType).toBe(PaymentType.USER);
      expect(dbPayment.userId).toBe(PaymentFixtures.TEST_USER_ID);
      expect(dbPayment.status).toBe('pending');

      // Verify Stripe integration
      expect(mockStripe.checkout.sessions.create).toHaveBeenCalledWith(
        expect.objectContaining({
          payment_method_types: ['card'],
          mode: 'payment',
          success_url: expect.stringContaining('success'),
          cancel_url: expect.stringContaining('cancel'),
          metadata: expect.objectContaining({
            paymentId: paymentResult.paymentId,
            serviceType: ServiceType.SERVICE,
          }),
        })
      );

      // Simulate webhook completion
      const webhookEvent = PaymentFixtures.getMockStripeWebhookEvent();
      webhookEvent.data.object.metadata.paymentId = paymentResult.paymentId;
      
      mockStripe.webhooks.constructEvent.mockReturnValue(webhookEvent);

      const webhookRequest = {
        body: Buffer.from(JSON.stringify(webhookEvent)),
        headers: { 'stripe-signature': 'test-signature' },
      };

      // Act - Process webhook
      const webhookResult = await paymentService.processWebhook(webhookRequest);

      // Assert - Webhook processing
      expect(webhookResult.received).toBe(true);

      // Verify payment completion in database
      const completedPayment = await prisma.payment.findUnique({
        where: { id: paymentResult.paymentId },
      });
      expect(completedPayment.status).toBe('completed');
      expect(completedPayment.stripePaymentIntentId).toBeDefined();
      expect(completedPayment.completedAt).toBeDefined();

      // Verify email notifications
      expect(mockMailer.sendPaymentConfirmation).toHaveBeenCalledWith(
        expect.objectContaining({
          id: paymentResult.paymentId,
          status: 'completed',
        })
      );
    });

    /**
     * Test: Guest Package Payment Complete Flow
     * Purpose: Verify end-to-end guest payment creation and processing
     * Dependencies: Database, Stripe, Email service
     */
    it('should complete guest package payment flow successfully', async () => {
      // Arrange
      const paymentDto = PaymentFixtures.getValidGuestPackagePayment();

      // Act - Create payment
      const paymentResult = await paymentService.createPayment(null, paymentDto);

      // Assert - Payment creation
      expect(paymentResult.status).toBe('success');
      expect(paymentResult.paymentId).toBeDefined();

      // Verify database record
      const dbPayment = await prisma.payment.findUnique({
        where: { id: paymentResult.paymentId },
      });
      expect(dbPayment).toBeDefined();
      expect(dbPayment.serviceType).toBe(ServiceType.PACKAGE);
      expect(dbPayment.paymentType).toBe(PaymentType.GUEST);
      expect(dbPayment.userId).toBeNull();
      expect(dbPayment.guestName).toBe('John Doe');
      expect(dbPayment.guestEmail).toBe('<EMAIL>');
      expect(dbPayment.guestMobile).toBe('+353871234567');

      // Verify Stripe session includes guest information
      expect(mockStripe.checkout.sessions.create).toHaveBeenCalledWith(
        expect.objectContaining({
          customer_email: '<EMAIL>',
          metadata: expect.objectContaining({
            guestName: 'John Doe',
            guestEmail: '<EMAIL>',
          }),
        })
      );
    });
  });

  describe('Database Transaction Integrity', () => {
    /**
     * Test: Payment Creation Transaction Rollback
     * Purpose: Verify database consistency when payment creation fails
     * Dependencies: Database transactions
     */
    it('should rollback transaction when Stripe session creation fails', async () => {
      // Arrange
      const paymentDto = PaymentFixtures.getValidUserServicePayment();
      const userPayload = { id: PaymentFixtures.TEST_USER_ID };
      
      // Mock Stripe failure
      mockStripe.checkout.sessions.create.mockRejectedValue(
        new Error('Stripe API error')
      );

      // Get initial payment count
      const initialCount = await prisma.payment.count();

      // Act & Assert
      await expect(
        paymentService.createPayment(userPayload, paymentDto)
      ).rejects.toThrow('Stripe API error');

      // Verify no payment record was created
      const finalCount = await prisma.payment.count();
      expect(finalCount).toBe(initialCount);
    });

    /**
     * Test: Concurrent Payment Creation
     * Purpose: Verify database handles concurrent payment creation correctly
     * Dependencies: Database concurrency control
     */
    it('should handle concurrent payment creation without conflicts', async () => {
      // Arrange
      const paymentDto = PaymentFixtures.getValidUserServicePayment();
      const userPayload = { id: PaymentFixtures.TEST_USER_ID };

      // Mock different Stripe sessions for each payment
      mockStripe.checkout.sessions.create
        .mockResolvedValueOnce({
          ...PaymentFixtures.getMockStripeSession(),
          id: 'cs_session_1',
        })
        .mockResolvedValueOnce({
          ...PaymentFixtures.getMockStripeSession(),
          id: 'cs_session_2',
        });

      // Act - Create payments concurrently
      const [payment1, payment2] = await Promise.all([
        paymentService.createPayment(userPayload, paymentDto),
        paymentService.createPayment(userPayload, paymentDto),
      ]);

      // Assert - Both payments created successfully
      expect(payment1.status).toBe('success');
      expect(payment2.status).toBe('success');
      expect(payment1.paymentId).not.toBe(payment2.paymentId);

      // Verify both records in database
      const dbPayments = await prisma.payment.findMany({
        where: {
          id: { in: [payment1.paymentId, payment2.paymentId] },
        },
      });
      expect(dbPayments).toHaveLength(2);
    });
  });

  describe('Error Handling and Recovery', () => {
    /**
     * Test: Service Not Found Error Handling
     * Purpose: Verify proper error handling when referenced service doesn't exist
     * Dependencies: Database foreign key constraints
     */
    it('should handle service not found error gracefully', async () => {
      // Arrange
      const paymentDto = {
        ...PaymentFixtures.getValidUserServicePayment(),
        serviceId: 'non-existent-service-id',
      };
      const userPayload = { id: PaymentFixtures.TEST_USER_ID };

      // Act & Assert
      await expect(
        paymentService.createPayment(userPayload, paymentDto)
      ).rejects.toThrow('Service not found');

      // Verify no payment record was created
      const payments = await prisma.payment.findMany({
        where: { serviceId: 'non-existent-service-id' },
      });
      expect(payments).toHaveLength(0);
    });

    /**
     * Test: Webhook Signature Verification Failure
     * Purpose: Verify webhook security and error handling
     * Dependencies: Stripe webhook verification
     */
    it('should reject webhook with invalid signature', async () => {
      // Arrange
      mockStripe.webhooks.constructEvent.mockImplementation(() => {
        throw new Error('Invalid signature');
      });

      const webhookRequest = {
        body: Buffer.from('invalid-webhook-body'),
        headers: { 'stripe-signature': 'invalid-signature' },
      };

      // Act & Assert
      await expect(
        paymentService.processWebhook(webhookRequest)
      ).rejects.toThrow('Invalid signature');
    });
  });

  describe('Performance and Load Testing', () => {
    /**
     * Test: Payment Creation Performance
     * Purpose: Verify payment creation meets performance requirements
     * Dependencies: Database performance, Stripe API performance
     */
    it('should create payment within performance threshold', async () => {
      // Arrange
      const paymentDto = PaymentFixtures.getValidUserServicePayment();
      const userPayload = { id: PaymentFixtures.TEST_USER_ID };
      const performanceThreshold = 2000; // 2 seconds

      // Act
      const startTime = performance.now();
      const result = await paymentService.createPayment(userPayload, paymentDto);
      const endTime = performance.now();

      // Assert
      expect(result.status).toBe('success');
      expect(endTime - startTime).toBeLessThan(performanceThreshold);
    });

    /**
     * Test: Bulk Payment Analytics Performance
     * Purpose: Verify analytics queries perform well with large datasets
     * Dependencies: Database query optimization
     */
    it('should generate analytics efficiently with large dataset', async () => {
      // Arrange - Generate test data
      await DatabaseTestUtils.generateLoadTestData(1000);

      // Act
      const startTime = performance.now();
      const analytics = await paymentService.getPaymentAnalytics();
      const endTime = performance.now();

      // Assert
      expect(analytics.totalRevenue).toBeGreaterThan(0);
      expect(analytics.totalPayments).toBeGreaterThan(0);
      expect(endTime - startTime).toBeLessThan(5000); // 5 seconds threshold

      // Cleanup
      await DatabaseTestUtils.cleanupLoadTestData();
    });
  });

  describe('Data Consistency Validation', () => {
    /**
     * Test: Payment History Filtering Accuracy
     * Purpose: Verify payment history filtering returns accurate results
     * Dependencies: Database query logic
     */
    it('should filter payment history accurately', async () => {
      // Arrange - Create test payments of different types
      const userPayment = await paymentService.createPayment(
        { id: PaymentFixtures.TEST_USER_ID },
        PaymentFixtures.getValidUserServicePayment()
      );

      const guestPayment = await paymentService.createPayment(
        null,
        PaymentFixtures.getValidGuestPackagePayment()
      );

      // Act - Filter by payment type
      const userPayments = await paymentService.getPaymentHistory({
        paymentType: PaymentType.USER,
      });

      const guestPayments = await paymentService.getPaymentHistory({
        paymentType: PaymentType.GUEST,
      });

      // Assert
      expect(userPayments.some(p => p.id === userPayment.paymentId)).toBe(true);
      expect(userPayments.every(p => p.paymentType === PaymentType.USER)).toBe(true);
      
      expect(guestPayments.some(p => p.id === guestPayment.paymentId)).toBe(true);
      expect(guestPayments.every(p => p.paymentType === PaymentType.GUEST)).toBe(true);
    });

    /**
     * Test: Database Constraint Validation
     * Purpose: Verify database constraints are properly enforced
     * Dependencies: Database schema constraints
     */
    it('should enforce database constraints correctly', async () => {
      // Act & Assert
      const constraints = await DatabaseTestUtils.verifyPaymentConstraints();

      expect(constraints.foreignKeyConstraints).toBe(true);
      expect(constraints.uniqueConstraints).toBe(true);
      expect(constraints.checkConstraints).toBe(true);
    });
  });
});
