/**
 * Database Test Utilities
 *
 * Comprehensive utilities for database testing including setup, teardown,
 * data seeding, and transaction management for payment-related tests.
 *
 * Features:
 * - Test database setup and cleanup
 * - Transaction management for isolated tests
 * - Data seeding and fixtures
 * - Performance monitoring
 * - Migration testing utilities
 */

import { PrismaClient } from '@prisma/client';
import { PaymentFixtures } from '../fixtures/payment-fixtures';

export class DatabaseTestUtils {
  private static prisma: PrismaClient;
  private static isInitialized = false;

  /**
   * Initialize test database connection
   */
  static async initialize(): Promise<PrismaClient> {
    if (!this.isInitialized) {
      this.prisma = new PrismaClient({
        datasources: {
          db: {
            url:
              process.env.DATABASE_URL ||
              'postgresql://test:test@localhost:5432/careerireland_test',
          },
        },
        log: process.env.NODE_ENV === 'test' ? [] : ['query', 'error'],
      });

      await this.prisma.$connect();
      this.isInitialized = true;
    }
    return this.prisma;
  }

  /**
   * Clean up database connection
   */
  static async cleanup(): Promise<void> {
    if (this.prisma) {
      await this.prisma.$disconnect();
      this.isInitialized = false;
    }
  }

  /**
   * Clear all payment-related test data
   */
  static async clearPaymentData(): Promise<void> {
    const prisma = await this.initialize();

    // Delete in correct order to respect foreign key constraints
    await prisma.payment.deleteMany({
      where: {
        OR: [
          { id: { startsWith: 'test-' } },
          { id: { startsWith: 'payment-' } },
          { serviceId: { startsWith: 'test-' } },
          { userId: { startsWith: 'test-' } },
        ],
      },
    });

    // Clear related test data
    await prisma.service.deleteMany({
      where: { id: { startsWith: 'test-' } },
    });

    await prisma.packages.deleteMany({
      where: { id: { startsWith: 'test-' } },
    });

    await prisma.immigration_service.deleteMany({
      where: { id: { startsWith: 'test-' } },
    });

    await prisma.training.deleteMany({
      where: { id: { startsWith: 'test-' } },
    });

    await prisma.user.deleteMany({
      where: { id: { startsWith: 'test-' } },
    });

    await prisma.mentor.deleteMany({
      where: { id: { startsWith: 'test-' } },
    });
  }

  /**
   * Seed test data for payment tests
   */
  static async seedPaymentTestData(): Promise<void> {
    const prisma = await this.initialize();

    // Create test user
    await prisma.user.upsert({
      where: { id: PaymentFixtures.TEST_USER_ID },
      update: {},
      create: {
        id: PaymentFixtures.TEST_USER_ID,
        name: 'Test User',
        email: '<EMAIL>',
        emailVerified: true,
        provider: 'credentials',
        password: 'hashed-password',
      },
    });

    // Create test mentor
    await prisma.mentor.upsert({
      where: { id: 'test-mentor-123' },
      update: {},
      create: {
        id: 'test-mentor-123',
        name: 'Test Mentor',
        email: '<EMAIL>',
        emailVerified: true,
        designation: 'Senior Developer',
        desc: 'Experienced software developer',
        image: 'mentor-image.jpg',
        status: 'Active',
      },
    });

    // Create test service
    await prisma.service.upsert({
      where: { id: PaymentFixtures.TEST_SERVICE_ID },
      update: {},
      create: PaymentFixtures.getMockService(),
    });

    // Create test package
    await prisma.packages.upsert({
      where: { id: PaymentFixtures.TEST_PACKAGE_ID },
      update: {},
      create: PaymentFixtures.getMockPackage(),
    });

    // Create test immigration service
    await prisma.immigration_service.upsert({
      where: { id: PaymentFixtures.TEST_IMMIGRATION_ID },
      update: {},
      create: PaymentFixtures.getMockImmigrationService(),
    });

    // Create test training
    await prisma.training.upsert({
      where: { id: PaymentFixtures.TEST_TRAINING_ID },
      update: {},
      create: PaymentFixtures.getMockTraining(),
    });
  }

  /**
   * Execute test within a transaction that gets rolled back
   */
  static async withTransaction<T>(
    testFn: (prisma: PrismaClient) => Promise<T>,
  ): Promise<T> {
    const prisma = await this.initialize();

    return await prisma
      .$transaction(async (tx) => {
        try {
          const result = await testFn(tx as PrismaClient);
          // Force rollback by throwing an error
          throw new Error('ROLLBACK_TRANSACTION');
        } catch (error) {
          if (error.message === 'ROLLBACK_TRANSACTION') {
            // This is our intentional rollback, return the result
            throw error;
          }
          // Re-throw actual errors
          throw error;
        }
      })
      .catch((error) => {
        if (error.message === 'ROLLBACK_TRANSACTION') {
          // Transaction was rolled back successfully
          return undefined as T;
        }
        throw error;
      });
  }

  /**
   * Create isolated test environment
   */
  static async createIsolatedTest<T>(
    testFn: (prisma: PrismaClient) => Promise<T>,
  ): Promise<T> {
    await this.clearPaymentData();
    await this.seedPaymentTestData();

    try {
      const prisma = await this.initialize();
      return await testFn(prisma);
    } finally {
      await this.clearPaymentData();
    }
  }

  /**
   * Verify database constraints
   */
  static async verifyPaymentConstraints(): Promise<{
    foreignKeyConstraints: boolean;
    uniqueConstraints: boolean;
    checkConstraints: boolean;
  }> {
    const prisma = await this.initialize();

    try {
      // Test foreign key constraints
      let foreignKeyConstraints = true;
      try {
        await prisma.payment.create({
          data: {
            ...PaymentFixtures.getMockUserPayment(),
            id: 'test-fk-constraint',
            serviceId: 'non-existent-service',
          },
        });
        foreignKeyConstraints = false; // Should have failed
      } catch (error) {
        // Expected to fail due to foreign key constraint
      }

      // Test unique constraints
      let uniqueConstraints = true;
      try {
        const payment1 = await prisma.payment.create({
          data: {
            ...PaymentFixtures.getMockUserPayment(),
            id: 'test-unique-1',
            stripeSessionId: 'unique-session-id',
          },
        });

        await prisma.payment.create({
          data: {
            ...PaymentFixtures.getMockUserPayment(),
            id: 'test-unique-2',
            stripeSessionId: 'unique-session-id', // Duplicate session ID
          },
        });
        uniqueConstraints = false; // Should have failed
      } catch (error) {
        // Expected to fail due to unique constraint
      }

      // Test check constraints (e.g., positive amounts)
      let checkConstraints = true;
      try {
        await prisma.payment.create({
          data: {
            ...PaymentFixtures.getMockUserPayment(),
            id: 'test-check-constraint',
            amount: -1000, // Negative amount should fail
          },
        });
        checkConstraints = false; // Should have failed
      } catch (error) {
        // Expected to fail due to check constraint
      }

      return {
        foreignKeyConstraints,
        uniqueConstraints,
        checkConstraints,
      };
    } finally {
      // Clean up test data
      await prisma.payment.deleteMany({
        where: {
          id: {
            in: [
              'test-fk-constraint',
              'test-unique-1',
              'test-unique-2',
              'test-check-constraint',
            ],
          },
        },
      });
    }
  }

  /**
   * Performance testing utilities
   */
  static async measureQueryPerformance<T>(
    queryFn: (prisma: PrismaClient) => Promise<T>,
    iterations: number = 1,
  ): Promise<{
    result: T;
    averageTime: number;
    minTime: number;
    maxTime: number;
    totalTime: number;
  }> {
    const prisma = await this.initialize();
    const times: number[] = [];
    let result: T;

    for (let i = 0; i < iterations; i++) {
      const startTime = performance.now();
      result = await queryFn(prisma);
      const endTime = performance.now();
      times.push(endTime - startTime);
    }

    return {
      result: result!,
      averageTime: times.reduce((a, b) => a + b, 0) / times.length,
      minTime: Math.min(...times),
      maxTime: Math.max(...times),
      totalTime: times.reduce((a, b) => a + b, 0),
    };
  }

  /**
   * Generate test data for load testing
   */
  static async generateLoadTestData(paymentCount: number): Promise<void> {
    const prisma = await this.initialize();
    const batchSize = 100;

    for (let i = 0; i < paymentCount; i += batchSize) {
      const batch = Math.min(batchSize, paymentCount - i);
      const payments = Array.from({ length: batch }, (_, index) => ({
        id: `load-test-payment-${i + index}`,
        service_type: 'service',
        serviceId: PaymentFixtures.TEST_SERVICE_ID,
        payment_type: 'user',
        userId: PaymentFixtures.TEST_USER_ID,
        amount: Math.floor(Math.random() * 50000) + 1000,
        status: Math.random() > 0.5 ? 'completed' : 'pending',
        stripe_session_id: `cs_load_test_${i + index}`,
        createdAt: new Date(
          Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000,
        ),
      }));

      await prisma.payment.createMany({
        data: payments,
        skipDuplicates: true,
      });
    }
  }

  /**
   * Clean up load test data
   */
  static async cleanupLoadTestData(): Promise<void> {
    const prisma = await this.initialize();

    await prisma.payment.deleteMany({
      where: {
        id: { startsWith: 'load-test-payment-' },
      },
    });
  }

  /**
   * Verify data integrity after operations
   */
  static async verifyDataIntegrity(): Promise<{
    orphanedPayments: number;
    invalidAmounts: number;
    missingRequiredFields: number;
  }> {
    const prisma = await this.initialize();

    // Check for orphaned payments (payments without valid service references)
    const orphanedPayments = await prisma.payment.count({
      where: {
        AND: [
          { serviceId: { not: null } },
          {
            OR: [
              {
                AND: [{ service_type: 'service' }, { service: null }],
              },
              {
                AND: [{ service_type: 'package' }, { package: null }],
              },
              {
                AND: [
                  { service_type: 'immigration' },
                  { immigration_service: null },
                ],
              },
              {
                AND: [{ service_type: 'training' }, { training: null }],
              },
            ],
          },
        ],
      },
    });

    // Check for invalid amounts
    const invalidAmounts = await prisma.payment.count({
      where: {
        OR: [{ amount: { lte: 0 } }, { amount: null }],
      },
    });

    // Check for missing required fields
    const missingRequiredFields = await prisma.payment.count({
      where: {
        OR: [{ service_type: null }, { payment_type: null }, { status: null }],
      },
    });

    return {
      orphanedPayments,
      invalidAmounts,
      missingRequiredFields,
    };
  }
}
