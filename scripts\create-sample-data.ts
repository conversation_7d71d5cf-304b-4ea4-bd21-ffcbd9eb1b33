/**
 * Create Sample Data Script
 *
 * This script creates sample users, services, packages, immigration services,
 * and training data that the payment test data script can reference.
 *
 * Usage: npm run create:sample-data
 */

import { PrismaClient } from '@prisma/client';
import { hash } from 'bcrypt';

const prisma = new PrismaClient();

function generateCuid(): string {
  const timestamp = Date.now().toString(36);
  const randomPart = Math.random().toString(36).substring(2, 8);
  return `cl${timestamp}${randomPart}`;
}

async function createSampleUsers() {
  console.log('👥 Creating sample users...');

  const users = [];
  for (let i = 1; i <= 20; i++) {
    const hashedPassword = await hash('password123', 10);
    const user = await prisma.user.create({
      data: {
        id: `user_${i.toString().padStart(3, '0')}`,
        name: `Test User ${i}`,
        email: `user${i}@test.com`,
        emailVerified: true,
        password: hashedPassword,
        provider: 'credentials',
      },
    });
    users.push(user);
  }

  console.log(`✅ Created ${users.length} sample users`);
  return users;
}

async function createSampleMentors() {
  console.log('👨‍🏫 Creating sample mentors...');

  const mentors = [];
  for (let i = 1; i <= 10; i++) {
    const hashedPassword = await hash('mentor123', 10);
    const mentor = await prisma.mentor.create({
      data: {
        id: `mentor_${i.toString().padStart(3, '0')}`,
        name: `Mentor ${i}`,
        email: `mentor${i}@test.com`,
        emailVerified: true,
        password: hashedPassword,
        image: `https://example.com/mentor${i}.jpg`,
        designation: `Senior ${['Consultant', 'Advisor', 'Specialist', 'Expert'][i % 4]}`,
        desc: `Experienced professional with expertise in career development and guidance. Mentor ${i} has helped hundreds of clients achieve their career goals.`,
        order: i,
        linkedin: `https://linkedin.com/in/mentor${i}`,
        profile: `https://example.com/profile/mentor${i}`,
        status: 'Active',
      },
    });
    mentors.push(mentor);
  }

  console.log(`✅ Created ${mentors.length} sample mentors`);
  return mentors;
}

async function createSampleServices(mentors: any[]) {
  console.log('🛠️ Creating sample services...');

  const serviceTypes = [
    {
      name: 'Career Consultation',
      price: 15000,
      desc: 'One-on-one career guidance session',
    },
    {
      name: 'Resume Review',
      price: 8000,
      desc: 'Professional resume review and feedback',
    },
    {
      name: 'Interview Preparation',
      price: 12000,
      desc: 'Mock interviews and preparation tips',
    },
    {
      name: 'LinkedIn Optimization',
      price: 10000,
      desc: 'LinkedIn profile optimization service',
    },
    {
      name: 'Career Planning',
      price: 20000,
      desc: 'Comprehensive career planning session',
    },
    {
      name: 'Salary Negotiation',
      price: 18000,
      desc: 'Salary negotiation strategies and tips',
    },
    {
      name: 'Job Search Strategy',
      price: 16000,
      desc: 'Personalized job search strategy',
    },
    {
      name: 'Networking Guidance',
      price: 14000,
      desc: 'Professional networking strategies',
    },
    {
      name: 'Skills Assessment',
      price: 11000,
      desc: 'Professional skills evaluation',
    },
    {
      name: 'Career Transition',
      price: 22000,
      desc: 'Career change guidance and support',
    },
  ];

  const services = [];
  for (let i = 0; i < serviceTypes.length; i++) {
    const serviceType = serviceTypes[i];
    const mentor = mentors[i % mentors.length];

    const service = await prisma.service.create({
      data: {
        id: `service_${(i + 1).toString().padStart(3, '0')}`,
        name: serviceType.name,
        price: serviceType.price,
        description: serviceType.desc,
        meeting_link: `https://meet.google.com/mentor${i + 1}`,
        mentorId: mentor.id,
        status: 'Active',
      },
    });
    services.push(service);
  }

  console.log(`✅ Created ${services.length} sample services`);
  return services;
}

async function createSamplePackages() {
  console.log('📦 Creating sample packages...');

  const packageTypes = [
    {
      name: 'Starter Package',
      note: 'Perfect for new job seekers',
      amount: 25000,
      services: ['Resume Review', 'Basic Career Consultation'],
    },
    {
      name: 'Professional Package',
      note: 'Comprehensive career development',
      amount: 45000,
      services: [
        'Resume Review',
        'Interview Preparation',
        'LinkedIn Optimization',
      ],
    },
    {
      name: 'Executive Package',
      note: 'For senior professionals',
      amount: 75000,
      services: ['Career Planning', 'Salary Negotiation', 'Executive Coaching'],
    },
    {
      name: 'Career Change Package',
      note: 'For career transitions',
      amount: 55000,
      services: [
        'Career Transition',
        'Skills Assessment',
        'Job Search Strategy',
      ],
    },
    {
      name: 'Graduate Package',
      note: 'For recent graduates',
      amount: 30000,
      services: [
        'Career Consultation',
        'Interview Preparation',
        'Networking Guidance',
      ],
    },
    {
      name: 'Premium Package',
      note: 'All-inclusive career support',
      amount: 95000,
      services: ['All Services', 'Priority Support', 'Follow-up Sessions'],
    },
    {
      name: 'Quick Start Package',
      note: 'Fast-track career boost',
      amount: 35000,
      services: ['Resume Review', 'LinkedIn Optimization', 'Job Search Tips'],
    },
    {
      name: 'Interview Master Package',
      note: 'Interview preparation focused',
      amount: 40000,
      services: [
        'Interview Preparation',
        'Mock Interviews',
        'Feedback Sessions',
      ],
    },
    {
      name: 'Networking Pro Package',
      note: 'Professional networking focus',
      amount: 38000,
      services: [
        'Networking Guidance',
        'LinkedIn Optimization',
        'Industry Connections',
      ],
    },
    {
      name: 'Leadership Package',
      note: 'For aspiring leaders',
      amount: 65000,
      services: [
        'Leadership Coaching',
        'Career Planning',
        'Executive Presence',
      ],
    },
  ];

  const packages = [];
  for (let i = 0; i < packageTypes.length; i++) {
    const packageType = packageTypes[i];

    const pkg = await prisma.packages.create({
      data: {
        id: `package_${(i + 1).toString().padStart(3, '0')}`,
        name: packageType.name,
        note: packageType.note,
        amount: packageType.amount,
        order: i + 1,
        service: packageType.services,
      },
    });
    packages.push(pkg);
  }

  console.log(`✅ Created ${packages.length} sample packages`);
  return packages;
}

async function createSampleImmigrationServices() {
  console.log('🛂 Creating sample immigration services...');

  const immigrationTypes = [
    {
      name: 'Work Permit Application',
      amount: 50000,
      services: [
        'Document Review',
        'Application Preparation',
        'Submission Support',
      ],
    },
    {
      name: 'Student Visa Assistance',
      amount: 35000,
      services: [
        'University Application',
        'Visa Documentation',
        'Interview Preparation',
      ],
    },
    {
      name: 'Family Reunification',
      amount: 60000,
      services: [
        'Eligibility Assessment',
        'Document Collection',
        'Application Processing',
      ],
    },
    {
      name: 'Permanent Residency',
      amount: 80000,
      services: [
        'Points Assessment',
        'Documentation',
        'Application Submission',
      ],
    },
    {
      name: 'Citizenship Application',
      amount: 70000,
      services: [
        'Eligibility Check',
        'Document Preparation',
        'Interview Support',
      ],
    },
    {
      name: 'Business Visa',
      amount: 90000,
      services: [
        'Business Plan Review',
        'Investment Documentation',
        'Application Support',
      ],
    },
    {
      name: 'Tourist Visa',
      amount: 25000,
      services: [
        'Application Form',
        'Supporting Documents',
        'Appointment Booking',
      ],
    },
    {
      name: 'Spouse Visa',
      amount: 55000,
      services: [
        'Relationship Evidence',
        'Financial Documentation',
        'Application Processing',
      ],
    },
    {
      name: 'Refugee Status',
      amount: 45000,
      services: [
        'Case Assessment',
        'Documentation Support',
        'Legal Representation',
      ],
    },
    {
      name: 'Visa Extension',
      amount: 30000,
      services: [
        'Current Status Review',
        'Extension Application',
        'Supporting Documents',
      ],
    },
  ];

  const immigrationServices = [];
  for (let i = 0; i < immigrationTypes.length; i++) {
    const immigrationType = immigrationTypes[i];

    const service = await prisma.immigration_service.create({
      data: {
        id: `immigration_${(i + 1).toString().padStart(3, '0')}`,
        name: immigrationType.name,
        amount: immigrationType.amount,
        order: i + 1,
        service: immigrationType.services,
      },
    });
    immigrationServices.push(service);
  }

  console.log(
    `✅ Created ${immigrationServices.length} sample immigration services`,
  );
  return immigrationServices;
}

async function createSampleTraining() {
  console.log('🎓 Creating sample training...');

  const trainingTypes = [
    {
      name: 'Digital Marketing Fundamentals',
      amount: 40000,
      img: 'https://example.com/training/digital-marketing.jpg',
      services: ['SEO Basics', 'Social Media Marketing', 'Content Strategy'],
      highlights: [
        'Industry Certified',
        'Hands-on Projects',
        'Job Placement Support',
      ],
    },
    {
      name: 'Data Analytics Bootcamp',
      amount: 60000,
      img: 'https://example.com/training/data-analytics.jpg',
      services: ['Python Programming', 'SQL Database', 'Data Visualization'],
      highlights: [
        'Real-world Projects',
        'Industry Mentors',
        'Portfolio Development',
      ],
    },
    {
      name: 'Project Management Professional',
      amount: 50000,
      img: 'https://example.com/training/project-management.jpg',
      services: [
        'PMP Certification Prep',
        'Agile Methodologies',
        'Risk Management',
      ],
      highlights: ['PMP Exam Prep', 'Case Studies', 'Networking Opportunities'],
    },
    {
      name: 'Web Development Full Stack',
      amount: 75000,
      img: 'https://example.com/training/web-development.jpg',
      services: ['Frontend Development', 'Backend APIs', 'Database Design'],
      highlights: ['Live Projects', 'Code Reviews', 'Job Guarantee'],
    },
    {
      name: 'Cybersecurity Essentials',
      amount: 55000,
      img: 'https://example.com/training/cybersecurity.jpg',
      services: ['Network Security', 'Ethical Hacking', 'Security Auditing'],
      highlights: ['Lab Environment', 'Industry Tools', 'Certification Path'],
    },
    {
      name: 'Business Analysis',
      amount: 45000,
      img: 'https://example.com/training/business-analysis.jpg',
      services: [
        'Requirements Gathering',
        'Process Modeling',
        'Stakeholder Management',
      ],
      highlights: ['Real Business Cases', 'Tools Training', 'Career Guidance'],
    },
    {
      name: 'Cloud Computing AWS',
      amount: 65000,
      img: 'https://example.com/training/cloud-computing.jpg',
      services: ['AWS Fundamentals', 'Cloud Architecture', 'DevOps Practices'],
      highlights: ['AWS Certification', 'Hands-on Labs', 'Industry Projects'],
    },
    {
      name: 'UX/UI Design',
      amount: 48000,
      img: 'https://example.com/training/ux-ui-design.jpg',
      services: ['User Research', 'Wireframing', 'Prototyping'],
      highlights: ['Design Portfolio', 'Industry Tools', 'Design Thinking'],
    },
    {
      name: 'Financial Analysis',
      amount: 52000,
      img: 'https://example.com/training/financial-analysis.jpg',
      services: [
        'Financial Modeling',
        'Investment Analysis',
        'Risk Assessment',
      ],
      highlights: ['Excel Mastery', 'Case Studies', 'Industry Insights'],
    },
    {
      name: 'Leadership Development',
      amount: 58000,
      img: 'https://example.com/training/leadership.jpg',
      services: [
        'Team Management',
        'Strategic Thinking',
        'Communication Skills',
      ],
      highlights: [
        '360 Feedback',
        'Coaching Sessions',
        'Leadership Assessment',
      ],
    },
  ];

  const training = [];
  for (let i = 0; i < trainingTypes.length; i++) {
    const trainingType = trainingTypes[i];

    const t = await prisma.training.create({
      data: {
        id: `training_${(i + 1).toString().padStart(3, '0')}`,
        name: trainingType.name,
        img: trainingType.img,
        amount: trainingType.amount,
        order: i + 1,
        service: trainingType.services,
        highlights: trainingType.highlights,
      },
    });
    training.push(t);
  }

  console.log(`✅ Created ${training.length} sample training programs`);
  return training;
}

async function main() {
  console.log('🚀 Creating sample data for payment testing...\n');

  try {
    // Create all sample data
    const users = await createSampleUsers();
    const mentors = await createSampleMentors();
    const services = await createSampleServices(mentors);
    const packages = await createSamplePackages();
    const immigrationServices = await createSampleImmigrationServices();
    const training = await createSampleTraining();

    console.log('\n🎉 Sample data creation completed!');
    console.log('📊 Summary:');
    console.log(`   Users: ${users.length}`);
    console.log(`   Mentors: ${mentors.length}`);
    console.log(`   Services: ${services.length}`);
    console.log(`   Packages: ${packages.length}`);
    console.log(`   Immigration Services: ${immigrationServices.length}`);
    console.log(`   Training Programs: ${training.length}`);
    console.log('\n✅ You can now run the payment test data script!');
    console.log('   Command: npm run seed:payment-data');
  } catch (error) {
    console.error('❌ Error creating sample data:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main().catch(console.error);
