import { Injectable } from '@nestjs/common';

import { Resend } from 'resend';
import { ResendEmailDto } from './dto/mailer.dto';

@Injectable()
export class MailerService {
  async sendEmail(dto: ResendEmailDto) {
    const { from, to, subject, html } = dto;
    const resend = new Resend(process.env.EMAIL_API_KEY);
    const { data, error } = await resend.emails.send({
      from: from,
      to: [to],
      subject: subject,
      html: html,
    });

    if (error) {
      console.error(error);
    }
    return data;
  }

  /**
   * Send payment confirmation email
   */
  async sendPaymentConfirmation(payment: any) {
    const emailData: ResendEmailDto = {
      from: process.env.FROM_EMAIL || '<EMAIL>',
      to:
        payment.guestEmail || payment.user?.email || '<EMAIL>',
      subject: 'Payment Confirmation - Career Ireland',
      html: `
        <h2>Payment Confirmation</h2>
        <p>Your payment has been successfully processed.</p>
        <p><strong>Payment ID:</strong> ${payment.id}</p>
        <p><strong>Amount:</strong> €${(payment.amount / 100).toFixed(2)}</p>
        <p><strong>Service:</strong> ${payment.serviceType}</p>
        <p>Thank you for your business!</p>
      `,
    };

    return this.sendEmail(emailData);
  }

  /**
   * Send admin notification email
   */
  async sendAdminNotification(payment: any) {
    const emailData: ResendEmailDto = {
      from: process.env.FROM_EMAIL || '<EMAIL>',
      to: process.env.ADMIN_EMAIL || '<EMAIL>',
      subject: 'New Payment Received - Career Ireland',
      html: `
        <h2>New Payment Notification</h2>
        <p>A new payment has been received.</p>
        <p><strong>Payment ID:</strong> ${payment.id}</p>
        <p><strong>Amount:</strong> €${(payment.amount / 100).toFixed(2)}</p>
        <p><strong>Service Type:</strong> ${payment.serviceType}</p>
        <p><strong>Payment Type:</strong> ${payment.paymentType}</p>
        <p><strong>Customer:</strong> ${payment.guestName || payment.user?.name || 'N/A'}</p>
        <p><strong>Email:</strong> ${payment.guestEmail || payment.user?.email || 'N/A'}</p>
      `,
    };

    return this.sendEmail(emailData);
  }

  /**
   * Send payment failure notification
   */
  async sendPaymentFailure(payment: any) {
    const emailData: ResendEmailDto = {
      from: process.env.FROM_EMAIL || '<EMAIL>',
      to:
        payment.guestEmail || payment.user?.email || '<EMAIL>',
      subject: 'Payment Failed - Career Ireland',
      html: `
        <h2>Payment Failed</h2>
        <p>Unfortunately, your payment could not be processed.</p>
        <p><strong>Payment ID:</strong> ${payment.id}</p>
        <p><strong>Amount:</strong> €${(payment.amount / 100).toFixed(2)}</p>
        <p>Please try again or contact support for assistance.</p>
      `,
    };

    return this.sendEmail(emailData);
  }
}
