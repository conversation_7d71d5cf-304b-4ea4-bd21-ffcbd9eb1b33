import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEmail, IsOptional, IsString } from 'class-validator';
export class ResendEmailDto {
  @ApiProperty()
  @IsString()
  subject: string;
  @ApiProperty()
  @IsString()
  @IsEmail()
  to: string;

  @ApiProperty()
  @IsString()
  from: string;
  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  @IsEmail({ each: true })
  cc?: string[];

  @ApiProperty()
  @IsString()
  html: string;

  // @ApiProperty({ required: false })
  // @IsString()
  // @IsOptional()
  // text: string;
}
