/**
 * Payment Data Transfer Objects (DTOs)
 *
 * This file contains all the DTOs used for payment-related API endpoints.
 * These DTOs provide input validation, API documentation, and type safety
 * for payment operations across different service types.
 *
 * Key Features:
 * - Input validation using class-validator decorators
 * - API documentation using Swagger decorators
 * - Type safety for payment operations
 * - Support for both authenticated and guest users
 *
 * DTO Hierarchy:
 * - GuestDto: Base class for guest user information
 * - Service-specific DTOs: Extend GuestDto for different service types
 * - Unified DTOs: New DTOs for unified payment API
 *
 * <AUTHOR> Ireland Development Team
 * @version 2.0.0
 * @since 2024-12-27
 */

import { ApiProperty, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsInt,
  IsEmail,
  IsEnum,
  IsOptional,
} from 'class-validator';

/**
 * Guest User Information DTO
 *
 * Base DTO containing guest user information required for non-authenticated
 * payment processing. This information is collected during checkout for
 * users who don't have an account.
 *
 * Used for:
 * - Guest payment processing
 * - Email notifications
 * - Contact information storage
 */
export class GuestDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string;
  @ApiProperty()
  @IsEmail()
  @IsNotEmpty()
  email: string;
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  mobile_no: string;
}
export class UserMentorServiceDto extends PartialType(GuestDto) {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  serviceId: string;
}
export class UserPackageServiceDto extends PartialType(GuestDto) {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  packageId: string;
}
export class UserImmigrationServiceDto extends PartialType(GuestDto) {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  immigration_serviceId: string;
}
export class UserTrainingServiceDto extends PartialType(GuestDto) {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  trainingId: string;
}

export class UserServiceDto extends PartialType(GuestDto) {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  serviceId: string;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  amount: number;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  status: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  userId: string;
}
export class UserPackageDto extends PartialType(GuestDto) {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  packageId: string;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  amount: number;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  status: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  userId: string;
}
export class UserImmigrationDto extends PartialType(GuestDto) {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  immigration_serviceId: string;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  amount: number;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  status: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  userId: string;
}
export class UserTrainingDto extends PartialType(GuestDto) {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  trainingId: string;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  amount: number;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  status: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  userId: string;
}

// ===== UNIFIED PAYMENT DTOs =====

/**
 * Service Type Enum
 * Defines the available service types for unified payments
 */
export enum ServiceType {
  SERVICE = 'service',
  PACKAGE = 'package',
  IMMIGRATION = 'immigration',
  TRAINING = 'training',
}

/**
 * Payment Type Enum
 * Defines whether the payment is from an authenticated user or guest
 */
export enum PaymentType {
  USER = 'user',
  GUEST = 'guest',
}

/**
 * Unified Create Payment DTO
 * Single DTO for creating payments across all service types
 */
export class CreateUnifiedPaymentDto {
  @ApiProperty({
    description: 'Type of service being purchased',
    enum: ServiceType,
    example: ServiceType.SERVICE,
  })
  @IsEnum(ServiceType)
  @IsNotEmpty()
  serviceType: ServiceType;

  @ApiProperty({ description: 'ID of the service being purchased' })
  @IsString()
  @IsNotEmpty()
  serviceId: string;

  @ApiProperty({
    description: 'Type of payment (user or guest)',
    enum: PaymentType,
    example: PaymentType.USER,
  })
  @IsEnum(PaymentType)
  @IsNotEmpty()
  paymentType: PaymentType;

  @ApiProperty({ description: 'Guest full name', required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ description: 'Guest email address', required: false })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiProperty({ description: 'Guest mobile number', required: false })
  @IsString()
  @IsOptional()
  mobile?: string;
}

/**
 * Payment Filters DTO
 * Used for filtering payment history and analytics
 */
export class PaymentFiltersDto {
  @ApiProperty({
    description: 'Filter by service type',
    enum: ServiceType,
    required: false,
  })
  @IsOptional()
  @IsEnum(ServiceType)
  serviceType?: ServiceType;

  @ApiProperty({
    description: 'Filter by payment type',
    enum: PaymentType,
    required: false,
  })
  @IsOptional()
  @IsEnum(PaymentType)
  paymentType?: PaymentType;

  @ApiProperty({ description: 'Filter by payment status', required: false })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiProperty({ description: 'Filter by user ID', required: false })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiProperty({
    description: 'Start date for date range filter',
    required: false,
  })
  @IsOptional()
  @IsString()
  startDate?: string;

  @ApiProperty({
    description: 'End date for date range filter',
    required: false,
  })
  @IsOptional()
  @IsString()
  endDate?: string;
}

/**
 * Payment Response DTO
 * Standard response format for payment operations
 */
export class PaymentResponseDto {
  @ApiProperty({ description: 'Operation status' })
  status: string;

  @ApiProperty({ description: 'Stripe checkout session URL', required: false })
  url?: string;

  @ApiProperty({ description: 'Payment ID', required: false })
  paymentId?: string;

  @ApiProperty({ description: 'Response message', required: false })
  message?: string;
}
