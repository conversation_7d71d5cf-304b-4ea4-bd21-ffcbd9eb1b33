/**
 * Unified Payment Service Unit Tests
 * 
 * Comprehensive unit tests for the UnifiedPaymentService class.
 * Tests cover all payment operations, validation, error handling,
 * and integration with external services.
 * 
 * Test Categories:
 * - Payment creation for all service types
 * - User vs guest payment flows
 * - Webhook processing
 * - Payment history and analytics
 * - Error handling and edge cases
 * - Input validation
 * - External service integration
 */

import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UnifiedPaymentService } from './unified-payment.service';
import { PrismaService } from '../utils/prisma.service';
import { MailerService } from '../mailer/mailer.service';
import { CreateUnifiedPaymentDto, ServiceType, PaymentType } from './dto/payment.dto';

describe('UnifiedPaymentService', () => {
  let service: UnifiedPaymentService;
  let prismaService: PrismaService;
  let mailerService: MailerService;
  let jwtService: JwtService;
  let mockStripe: any;

  // Mock data
  const mockUser = global.testUtils.createTestUser();
  const mockService = global.testUtils.createTestService();
  const mockPayment = global.testUtils.createTestPayment();

  beforeEach(async () => {
    // Create mock Stripe instance
    mockStripe = {
      checkout: {
        sessions: {
          create: jest.fn(),
          retrieve: jest.fn(),
        },
      },
      webhooks: {
        constructEvent: jest.fn(),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UnifiedPaymentService,
        {
          provide: PrismaService,
          useValue: {
            user: {
              findUnique: jest.fn(),
            },
            service: {
              findUnique: jest.fn(),
            },
            packages: {
              findUnique: jest.fn(),
            },
            immigration_service: {
              findUnique: jest.fn(),
            },
            training: {
              findUnique: jest.fn(),
            },
            payment: {
              create: jest.fn(),
              findMany: jest.fn(),
              findUnique: jest.fn(),
              update: jest.fn(),
              aggregate: jest.fn(),
              groupBy: jest.fn(),
            },
            $transaction: jest.fn(),
          },
        },
        {
          provide: MailerService,
          useValue: {
            sendPaymentConfirmation: jest.fn(),
            sendAdminNotification: jest.fn(),
          },
        },
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn(),
            verify: jest.fn(),
          },
        },
        {
          provide: 'STRIPE_CLIENT',
          useValue: mockStripe,
        },
      ],
    }).compile();

    service = module.get<UnifiedPaymentService>(UnifiedPaymentService);
    prismaService = module.get<PrismaService>(PrismaService);
    mailerService = module.get<MailerService>(MailerService);
    jwtService = module.get<JwtService>(JwtService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createPayment', () => {
    const validDto: CreateUnifiedPaymentDto = {
      serviceType: ServiceType.SERVICE,
      serviceId: 'test-service-id',
      paymentType: PaymentType.USER,
    };

    it('should create user payment successfully', async () => {
      // Arrange
      const user = global.testUtils.createJWTPayload();
      prismaService.service.findUnique = jest.fn().mockResolvedValue(mockService);
      prismaService.payment.create = jest.fn().mockResolvedValue(mockPayment);
      mockStripe.checkout.sessions.create.mockResolvedValue({
        id: 'cs_test_session',
        url: 'https://checkout.stripe.com/test',
      });

      // Act
      const result = await service.createPayment(user, validDto);

      // Assert
      expect(result).toEqual({
        status: 'success',
        url: 'https://checkout.stripe.com/test',
        paymentId: mockPayment.id,
      });
      expect(prismaService.service.findUnique).toHaveBeenCalledWith({
        where: { id: validDto.serviceId },
      });
      expect(prismaService.payment.create).toHaveBeenCalled();
      expect(mockStripe.checkout.sessions.create).toHaveBeenCalled();
    });

    it('should create guest payment successfully', async () => {
      // Arrange
      const guestDto: CreateUnifiedPaymentDto = {
        ...validDto,
        paymentType: PaymentType.GUEST,
        name: 'Guest User',
        email: '<EMAIL>',
        mobile: '+353123456789',
      };
      
      prismaService.service.findUnique = jest.fn().mockResolvedValue(mockService);
      prismaService.payment.create = jest.fn().mockResolvedValue({
        ...mockPayment,
        paymentType: 'guest',
        guestName: 'Guest User',
        guestEmail: '<EMAIL>',
        guestMobile: '+353123456789',
      });
      mockStripe.checkout.sessions.create.mockResolvedValue({
        id: 'cs_test_session',
        url: 'https://checkout.stripe.com/test',
      });

      // Act
      const result = await service.createPayment(null, guestDto);

      // Assert
      expect(result.status).toBe('success');
      expect(prismaService.payment.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          paymentType: 'guest',
          guestName: 'Guest User',
          guestEmail: '<EMAIL>',
          guestMobile: '+353123456789',
        }),
      });
    });

    it('should throw error when service not found', async () => {
      // Arrange
      const user = global.testUtils.createJWTPayload();
      prismaService.service.findUnique = jest.fn().mockResolvedValue(null);

      // Act & Assert
      await expect(service.createPayment(user, validDto)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw error for user payment without authentication', async () => {
      // Arrange
      const dto = { ...validDto, paymentType: PaymentType.USER };

      // Act & Assert
      await expect(service.createPayment(null, dto)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should throw error for guest payment without contact info', async () => {
      // Arrange
      const dto = { ...validDto, paymentType: PaymentType.GUEST };

      // Act & Assert
      await expect(service.createPayment(null, dto)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should handle different service types', async () => {
      // Test package service
      const packageDto = { ...validDto, serviceType: ServiceType.PACKAGE };
      const user = global.testUtils.createJWTPayload();
      
      prismaService.packages.findUnique = jest.fn().mockResolvedValue({
        id: 'package-id',
        name: 'Test Package',
        amount: 15000,
      });
      prismaService.payment.create = jest.fn().mockResolvedValue(mockPayment);
      mockStripe.checkout.sessions.create.mockResolvedValue({
        id: 'cs_test_session',
        url: 'https://checkout.stripe.com/test',
      });

      const result = await service.createPayment(user, packageDto);

      expect(result.status).toBe('success');
      expect(prismaService.packages.findUnique).toHaveBeenCalledWith({
        where: { id: packageDto.serviceId },
      });
    });
  });

  describe('processWebhook', () => {
    const mockRequest = {
      body: Buffer.from('test-webhook-body'),
      headers: {
        'stripe-signature': 'test-signature',
      },
    } as any;

    it('should process successful payment webhook', async () => {
      // Arrange
      const mockEvent = {
        type: 'checkout.session.completed',
        data: {
          object: {
            id: 'cs_test_session',
            payment_intent: 'pi_test_intent',
            metadata: {
              paymentId: mockPayment.id,
            },
          },
        },
      };

      mockStripe.webhooks.constructEvent.mockReturnValue(mockEvent);
      prismaService.payment.findUnique = jest.fn().mockResolvedValue(mockPayment);
      prismaService.payment.update = jest.fn().mockResolvedValue({
        ...mockPayment,
        status: 'completed',
      });

      // Act
      const result = await service.processWebhook(mockRequest);

      // Assert
      expect(result).toEqual({ received: true });
      expect(prismaService.payment.update).toHaveBeenCalledWith({
        where: { id: mockPayment.id },
        data: {
          status: 'completed',
          stripePaymentIntentId: 'pi_test_intent',
          completedAt: expect.any(Date),
        },
      });
      expect(mailerService.sendPaymentConfirmation).toHaveBeenCalled();
    });

    it('should handle webhook signature verification failure', async () => {
      // Arrange
      mockStripe.webhooks.constructEvent.mockImplementation(() => {
        throw new Error('Invalid signature');
      });

      // Act & Assert
      await expect(service.processWebhook(mockRequest)).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('getPaymentHistory', () => {
    it('should return filtered payment history', async () => {
      // Arrange
      const filters = {
        serviceType: ServiceType.SERVICE,
        paymentType: PaymentType.USER,
        status: 'completed',
      };
      const mockPayments = [mockPayment];
      
      prismaService.payment.findMany = jest.fn().mockResolvedValue(mockPayments);

      // Act
      const result = await service.getPaymentHistory(filters);

      // Assert
      expect(result).toEqual(mockPayments);
      expect(prismaService.payment.findMany).toHaveBeenCalledWith({
        where: {
          serviceType: ServiceType.SERVICE,
          paymentType: PaymentType.USER,
          status: 'completed',
        },
        include: expect.any(Object),
        orderBy: { createdAt: 'desc' },
      });
    });
  });

  describe('getPaymentAnalytics', () => {
    it('should return comprehensive analytics', async () => {
      // Arrange
      const mockAnalytics = {
        totalRevenue: 100000,
        totalPayments: 50,
        revenueByType: [
          { paymentType: 'user', _sum: { amount: 80000 }, _count: 40 },
          { paymentType: 'guest', _sum: { amount: 20000 }, _count: 10 },
        ],
        revenueByService: [
          { serviceType: 'service', _sum: { amount: 60000 }, _count: 30 },
          { serviceType: 'package', _sum: { amount: 40000 }, _count: 20 },
        ],
      };

      prismaService.payment.aggregate = jest.fn().mockResolvedValue({
        _sum: { amount: 100000 },
        _count: 50,
      });
      prismaService.payment.groupBy = jest.fn()
        .mockResolvedValueOnce(mockAnalytics.revenueByType)
        .mockResolvedValueOnce(mockAnalytics.revenueByService);

      // Act
      const result = await service.getPaymentAnalytics();

      // Assert
      expect(result).toEqual({
        totalRevenue: 100000,
        totalPayments: 50,
        revenueByPaymentType: mockAnalytics.revenueByType,
        revenueByServiceType: mockAnalytics.revenueByService,
      });
    });
  });
});
