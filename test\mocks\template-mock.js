/**
 * Template Mock for Jest Tests
 * 
 * This mock replaces the React email templates during testing
 * to avoid JSX/TSX compilation issues in Jest.
 */

// Mock function that returns a simple HTML string
const mockTemplate = (props) => {
  return `<html><body>Mock Email Template</body></html>`;
};

// Export as default (for default imports)
module.exports = mockTemplate;

// Also export as named export (for named imports)
module.exports.default = mockTemplate;
module.exports.MentorPaymentSuccessEmail = mockTemplate;
module.exports.PurchaseNotificationEmail = mockTemplate;
