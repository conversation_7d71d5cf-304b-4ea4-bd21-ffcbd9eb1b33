/**
 * User Service Unit Tests
 * 
 * Comprehensive unit tests for the UserService class.
 * Tests cover user registration, authentication, profile management,
 * password operations, and admin functions.
 * 
 * Test Categories:
 * - User registration and creation
 * - Authentication and login
 * - Profile management
 * - Password operations
 * - Admin user management
 * - Email verification
 * - Error handling and validation
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ConflictException, UnauthorizedException, NotFoundException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { UserService } from './user.service';
import { PrismaService } from '../utils/prisma.service';
import { OtpService } from '../otp/otp.service';
import { MailerService } from '../mailer/mailer.service';
import { CreateUserDto, LoginUserDto, UpdateUserDto } from './dto/user.dto';

// Mock bcrypt
jest.mock('bcrypt');
const mockBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;

describe('UserService', () => {
  let service: UserService;
  let prismaService: PrismaService;
  let jwtService: JwtService;
  let otpService: OtpService;
  let mailerService: MailerService;

  // Mock data
  const mockUser = global.testUtils.createTestUser();
  const mockJWTPayload = global.testUtils.createJWTPayload();

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: PrismaService,
          useValue: {
            user: {
              findUnique: jest.fn(),
              create: jest.fn(),
              update: jest.fn(),
              delete: jest.fn(),
              findMany: jest.fn(),
            },
          },
        },
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn(),
            verify: jest.fn(),
          },
        },
        {
          provide: OtpService,
          useValue: {
            generateOTPToken: jest.fn(),
            verifyOTPToken: jest.fn(),
          },
        },
        {
          provide: MailerService,
          useValue: {
            sendVerificationEmail: jest.fn(),
            sendPasswordResetEmail: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    prismaService = module.get<PrismaService>(PrismaService);
    jwtService = module.get<JwtService>(JwtService);
    otpService = module.get<OtpService>(OtpService);
    mailerService = module.get<MailerService>(MailerService);

    // Setup bcrypt mocks
    mockBcrypt.hash.mockResolvedValue('hashed-password' as never);
    mockBcrypt.compare.mockResolvedValue(true as never);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    const createUserDto: CreateUserDto = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
    };

    it('should create user successfully', async () => {
      // Arrange
      prismaService.user.findUnique = jest.fn().mockResolvedValue(null);
      prismaService.user.create = jest.fn().mockResolvedValue(mockUser);
      otpService.generateOTPToken = jest.fn().mockResolvedValue({
        token: 'otp-token',
        expiresAt: new Date(),
      });

      // Act
      const result = await service.create(createUserDto);

      // Assert
      expect(result).toEqual({
        token: 'otp-token',
        status: 'Ok',
        message: 'Verify your email address',
      });
      expect(prismaService.user.findUnique).toHaveBeenCalledWith({
        where: { email: createUserDto.email },
      });
      expect(prismaService.user.create).toHaveBeenCalledWith({
        data: {
          ...createUserDto,
          provider: 'credentials',
          password: 'hashed-password',
        },
      });
      expect(mockBcrypt.hash).toHaveBeenCalledWith(createUserDto.password, 10);
    });

    it('should throw ConflictException if user already exists', async () => {
      // Arrange
      prismaService.user.findUnique = jest.fn().mockResolvedValue(mockUser);

      // Act & Assert
      await expect(service.create(createUserDto)).rejects.toThrow(
        ConflictException,
      );
      expect(prismaService.user.create).not.toHaveBeenCalled();
    });

    it('should handle password hashing errors', async () => {
      // Arrange
      prismaService.user.findUnique = jest.fn().mockResolvedValue(null);
      mockBcrypt.hash.mockRejectedValue(new Error('Hashing failed') as never);

      // Act & Assert
      await expect(service.create(createUserDto)).rejects.toThrow(
        'Hashing failed',
      );
    });
  });

  describe('login', () => {
    const loginDto: LoginUserDto = {
      email: '<EMAIL>',
      password: 'password123',
    };

    it('should login user successfully', async () => {
      // Arrange
      const userWithPassword = { ...mockUser, password: 'hashed-password' };
      prismaService.user.findUnique = jest.fn().mockResolvedValue(userWithPassword);
      jwtService.sign = jest.fn()
        .mockReturnValueOnce('access-token')
        .mockReturnValueOnce('refresh-token');

      // Act
      const result = await service.login(loginDto);

      // Assert
      expect(result).toEqual({
        access_token: 'access-token',
        refresh_token: 'refresh-token',
        user: expect.objectContaining({
          id: mockUser.id,
          email: mockUser.email,
          name: mockUser.name,
        }),
      });
      expect(mockBcrypt.compare).toHaveBeenCalledWith(
        loginDto.password,
        'hashed-password',
      );
    });

    it('should throw UnauthorizedException for invalid email', async () => {
      // Arrange
      prismaService.user.findUnique = jest.fn().mockResolvedValue(null);

      // Act & Assert
      await expect(service.login(loginDto)).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it('should throw UnauthorizedException for invalid password', async () => {
      // Arrange
      const userWithPassword = { ...mockUser, password: 'hashed-password' };
      prismaService.user.findUnique = jest.fn().mockResolvedValue(userWithPassword);
      mockBcrypt.compare.mockResolvedValue(false as never);

      // Act & Assert
      await expect(service.login(loginDto)).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it('should throw UnauthorizedException for unverified email', async () => {
      // Arrange
      const unverifiedUser = { ...mockUser, emailVerified: false, password: 'hashed-password' };
      prismaService.user.findUnique = jest.fn().mockResolvedValue(unverifiedUser);

      // Act & Assert
      await expect(service.login(loginDto)).rejects.toThrow(
        UnauthorizedException,
      );
    });
  });

  describe('removeAccount', () => {
    it('should delete user account successfully', async () => {
      // Arrange
      prismaService.user.delete = jest.fn().mockResolvedValue(mockUser);

      // Act
      const result = await service.removeAccount(mockJWTPayload);

      // Assert
      expect(result).toEqual(mockUser);
      expect(prismaService.user.delete).toHaveBeenCalledWith({
        where: { id: mockJWTPayload.id },
      });
    });

    it('should handle deletion errors', async () => {
      // Arrange
      prismaService.user.delete = jest.fn().mockRejectedValue(
        new Error('User not found'),
      );

      // Act & Assert
      await expect(service.removeAccount(mockJWTPayload)).rejects.toThrow(
        'User not found',
      );
    });
  });

  describe('updateProfile', () => {
    const updateDto: UpdateUserDto = {
      name: 'Updated Name',
      email: '<EMAIL>',
    };

    it('should update user profile successfully', async () => {
      // Arrange
      const updatedUser = { ...mockUser, ...updateDto };
      prismaService.user.update = jest.fn().mockResolvedValue(updatedUser);

      // Act
      const result = await service.updateProfile(mockJWTPayload.id, updateDto);

      // Assert
      expect(result).toEqual(updatedUser);
      expect(prismaService.user.update).toHaveBeenCalledWith({
        where: { id: mockJWTPayload.id },
        data: updateDto,
      });
    });

    it('should update password when provided', async () => {
      // Arrange
      const updateWithPassword = { ...updateDto, password: 'newpassword123' };
      const updatedUser = { ...mockUser, ...updateWithPassword };
      prismaService.user.update = jest.fn().mockResolvedValue(updatedUser);

      // Act
      const result = await service.updateProfile(mockJWTPayload.id, updateWithPassword);

      // Assert
      expect(mockBcrypt.hash).toHaveBeenCalledWith('newpassword123', 10);
      expect(prismaService.user.update).toHaveBeenCalledWith({
        where: { id: mockJWTPayload.id },
        data: {
          ...updateDto,
          password: 'hashed-password',
        },
      });
    });
  });

  describe('adminRegister', () => {
    const adminUserDto: CreateUserDto = {
      name: 'Admin User',
      email: '<EMAIL>',
      password: 'adminpassword123',
    };

    it('should create admin user with verified email', async () => {
      // Arrange
      prismaService.user.findUnique = jest.fn().mockResolvedValue(null);
      const adminUser = { ...mockUser, emailVerified: true };
      prismaService.user.create = jest.fn().mockResolvedValue(adminUser);

      // Act
      const result = await service.adminRegister(adminUserDto);

      // Assert
      expect(result).toEqual(adminUser);
      expect(prismaService.user.create).toHaveBeenCalledWith({
        data: {
          ...adminUserDto,
          provider: 'credentials',
          emailVerified: true,
          password: 'hashed-password',
        },
      });
    });

    it('should throw ConflictException if admin email exists', async () => {
      // Arrange
      prismaService.user.findUnique = jest.fn().mockResolvedValue(mockUser);

      // Act & Assert
      await expect(service.adminRegister(adminUserDto)).rejects.toThrow(
        ConflictException,
      );
    });
  });

  describe('refreshToken', () => {
    it('should generate new access token', async () => {
      // Arrange
      jwtService.verify = jest.fn().mockReturnValue(mockJWTPayload);
      jwtService.sign = jest.fn().mockReturnValue('new-access-token');

      // Act
      const result = await service.refreshToken('valid-refresh-token');

      // Assert
      expect(result).toEqual({
        access_token: 'new-access-token',
      });
      expect(jwtService.verify).toHaveBeenCalledWith('valid-refresh-token');
    });

    it('should throw UnauthorizedException for invalid refresh token', async () => {
      // Arrange
      jwtService.verify = jest.fn().mockImplementation(() => {
        throw new Error('Invalid token');
      });

      // Act & Assert
      await expect(service.refreshToken('invalid-token')).rejects.toThrow(
        UnauthorizedException,
      );
    });
  });
});
