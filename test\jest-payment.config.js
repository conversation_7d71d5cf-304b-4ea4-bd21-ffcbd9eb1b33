/**
 * Jest Configuration for Payment Tests
 * 
 * Specialized Jest configuration for payment-related tests with
 * optimized settings for database testing, performance monitoring,
 * and comprehensive coverage reporting.
 * 
 * Features:
 * - Separate test environments for different test types
 * - Database setup and teardown hooks
 * - Performance monitoring
 * - Custom matchers for payment testing
 * - Detailed coverage reporting
 */

module.exports = {
  // Test environment configuration
  testEnvironment: 'node',
  
  // Test file patterns
  testMatch: [
    '**/test/payment/**/*.spec.ts',
    '**/test/database/**/*.spec.ts',
  ],
  
  // Module file extensions
  moduleFileExtensions: ['js', 'json', 'ts'],
  
  // Transform configuration
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  
  // Module name mapping
  moduleNameMapping: {
    '^src/(.*)$': '<rootDir>/src/$1',
    '^test/(.*)$': '<rootDir>/test/$1',
  },
  
  // Setup files
  setupFilesAfterEnv: [
    '<rootDir>/test/setup.ts',
    '<rootDir>/test/payment/payment-test-setup.ts',
  ],
  
  // Coverage configuration
  collectCoverageFrom: [
    'src/payment/**/*.ts',
    'src/utils/prisma.service.ts',
    'src/mailer/**/*.ts',
    '!**/*.module.ts',
    '!**/*.interface.ts',
    '!**/*.dto.ts',
    '!**/main.ts',
  ],
  
  // Coverage thresholds specifically for payment features
  coverageThreshold: {
    global: {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85,
    },
    // Stricter thresholds for critical payment components
    'src/payment/unified-payment.service.ts': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90,
    },
    'src/payment/unified-payment.controller.ts': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90,
    },
  },
  
  // Coverage reporters
  coverageReporters: [
    'text',
    'lcov',
    'html',
    'json-summary',
  ],
  
  // Coverage directory
  coverageDirectory: '<rootDir>/coverage/payment',
  
  // Test timeout (increased for database tests)
  testTimeout: 30000,
  
  // Global setup and teardown
  globalSetup: '<rootDir>/test/global-setup.ts',
  globalTeardown: '<rootDir>/test/global-teardown.ts',
  
  // Verbose output for detailed test results
  verbose: true,
  
  // Detect open handles (useful for database connections)
  detectOpenHandles: true,
  
  // Force exit after tests complete
  forceExit: true,
  
  // Maximum worker processes for parallel testing
  maxWorkers: 4,
  
  // Test result processor for custom reporting
  testResultsProcessor: '<rootDir>/test/utils/test-results-processor.js',
  
  // Custom reporters
  reporters: [
    'default',
    [
      'jest-html-reporters',
      {
        publicPath: './coverage/payment/html-report',
        filename: 'payment-test-report.html',
        expand: true,
        hideIcon: false,
        pageTitle: 'Payment System Test Report',
        logoImgPath: undefined,
        inlineSource: false,
      },
    ],
    [
      'jest-junit',
      {
        outputDirectory: './coverage/payment',
        outputName: 'payment-test-results.xml',
        suiteName: 'Payment System Tests',
        classNameTemplate: '{classname}',
        titleTemplate: '{title}',
        ancestorSeparator: ' › ',
        usePathForSuiteName: true,
      },
    ],
  ],
  
  // Performance monitoring
  slowTestThreshold: 5000, // 5 seconds
  
  // Error handling
  errorOnDeprecated: true,
  
  // Cache configuration
  cache: true,
  cacheDirectory: '<rootDir>/node_modules/.cache/jest/payment',
  
  // Watch mode configuration
  watchPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/dist/',
    '<rootDir>/coverage/',
  ],
  
  // Test environment options
  testEnvironmentOptions: {
    NODE_ENV: 'test',
    DATABASE_URL: 'postgresql://test:test@localhost:5432/careerireland_test',
  },
  
  // Custom matchers and utilities
  setupFiles: ['<rootDir>/test/utils/custom-matchers.ts'],
  
  // Ignore patterns
  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/dist/',
  ],
  
  // Transform ignore patterns
  transformIgnorePatterns: [
    'node_modules/(?!(.*\\.mjs$))',
  ],
  
  // Collect coverage from specific patterns
  collectCoverageFrom: [
    'src/payment/**/*.{ts,js}',
    'src/utils/prisma.service.{ts,js}',
    'src/mailer/**/*.{ts,js}',
    '!src/**/*.d.ts',
    '!src/**/*.module.ts',
    '!src/**/*.interface.ts',
    '!src/**/dto/*.ts',
    '!src/main.ts',
  ],
  
  // Snapshot serializers
  snapshotSerializers: [
    '<rootDir>/test/utils/payment-snapshot-serializer.js',
  ],
  
  // Module directories
  moduleDirectories: ['node_modules', '<rootDir>/src', '<rootDir>/test'],
  
  // Resolver configuration
  resolver: '<rootDir>/test/utils/jest-resolver.js',
  
  // Clear mocks between tests
  clearMocks: true,
  
  // Restore mocks after each test
  restoreMocks: true,
  
  // Reset modules between tests
  resetModules: false,
  
  // Bail configuration (stop after first test failure in CI)
  bail: process.env.CI ? 1 : 0,
  
  // Notify configuration
  notify: !process.env.CI,
  notifyMode: 'failure-change',
  
  // Colors in output
  colors: true,
  
  // Silent mode
  silent: false,
  
  // Log heap usage
  logHeapUsage: process.env.NODE_ENV === 'development',
  
  // Preset configuration
  preset: 'ts-jest',
  
  // Globals configuration for ts-jest
  globals: {
    'ts-jest': {
      tsconfig: {
        compilerOptions: {
          module: 'commonjs',
          target: 'es2020',
          lib: ['es2020'],
          experimentalDecorators: true,
          emitDecoratorMetadata: true,
          allowSyntheticDefaultImports: true,
          esModuleInterop: true,
          skipLibCheck: true,
          strict: true,
          forceConsistentCasingInFileNames: true,
          moduleResolution: 'node',
          resolveJsonModule: true,
          isolatedModules: true,
          noEmit: true,
          baseUrl: '.',
          paths: {
            'src/*': ['src/*'],
            'test/*': ['test/*'],
          },
        },
      },
      isolatedModules: true,
      useESM: false,
    },
  },
  
  // Extension configuration
  extensionsToTreatAsEsm: ['.ts'],
  
  // Watch plugins
  watchPlugins: [
    'jest-watch-typeahead/filename',
    'jest-watch-typeahead/testname',
  ],
};
