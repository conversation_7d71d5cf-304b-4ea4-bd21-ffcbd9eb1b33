/**
 * Test Script for Unified Payment System
 *
 * This script tests the new unified payment system to ensure it works correctly
 * before deploying to production. It validates the key functionality including:
 * - Payment creation for different service types
 * - Database operations
 * - Email notifications (dry run)
 * - Analytics and reporting
 *
 * Usage: npx ts-node scripts/test-unified-payment.ts
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */

import { PrismaClient } from '@prisma/client';
import { ServiceType, PaymentType } from '../src/payment/dto/payment.dto';

const prisma = new PrismaClient();

interface TestResult {
  test: string;
  status: 'PASS' | 'FAIL';
  message: string;
  duration?: number;
}

class UnifiedPaymentTester {
  private results: TestResult[] = [];

  async runAllTests(): Promise<void> {
    console.log('🚀 Starting Unified Payment System Tests...\n');

    await this.testDatabaseConnection();
    await this.testUnifiedPaymentTable();
    await this.testPaymentCreation();
    await this.testPaymentQueries();
    await this.testAnalytics();

    this.printResults();
  }

  private async testDatabaseConnection(): Promise<void> {
    const startTime = Date.now();
    try {
      await prisma.$connect();
      this.addResult(
        'Database Connection',
        'PASS',
        'Successfully connected to database',
        Date.now() - startTime,
      );
    } catch (error) {
      this.addResult(
        'Database Connection',
        'FAIL',
        `Failed to connect: ${error.message}`,
        Date.now() - startTime,
      );
    }
  }

  private async testUnifiedPaymentTable(): Promise<void> {
    const startTime = Date.now();
    try {
      // Test if unified payment table exists and has correct structure
      const tableInfo = await prisma.$queryRaw`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_name = 'payment'
        ORDER BY ordinal_position;
      `;

      const requiredColumns = [
        'id',
        'amount',
        'status',
        'payment_type',
        'service_type',
        'progress',
        'userId',
        'serviceId',
        'packageId',
        'immigration_serviceId',
        'trainingId',
        'guest_name',
        'guest_email',
        'guest_mobile',
        'stripe_session_id',
        'stripe_payment_intent_id',
        'createdAt',
        'updatedAt',
      ];

      const existingColumns = (tableInfo as any[]).map(
        (col) => col.column_name,
      );
      const missingColumns = requiredColumns.filter(
        (col) => !existingColumns.includes(col),
      );

      if (missingColumns.length === 0) {
        this.addResult(
          'Unified Payment Table Structure',
          'PASS',
          `All ${requiredColumns.length} required columns present`,
          Date.now() - startTime,
        );
      } else {
        this.addResult(
          'Unified Payment Table Structure',
          'FAIL',
          `Missing columns: ${missingColumns.join(', ')}`,
          Date.now() - startTime,
        );
      }
    } catch (error) {
      this.addResult(
        'Unified Payment Table Structure',
        'FAIL',
        `Error checking table: ${error.message}`,
        Date.now() - startTime,
      );
    }
  }

  private async testPaymentCreation(): Promise<void> {
    const startTime = Date.now();
    try {
      // Test creating payments for different service types
      const testPayments = [
        {
          amount: 100,
          status: 'test',
          payment_type: PaymentType.USER,
          service_type: ServiceType.SERVICE,
          serviceId: 'test-mentor-service-id',
          userId: 'test-user-id',
        },
        {
          amount: 200,
          status: 'test',
          payment_type: PaymentType.GUEST,
          service_type: ServiceType.PACKAGE,
          packageId: 'test-package-id',
          guest_name: 'Test Guest',
          guest_email: '<EMAIL>',
          guest_mobile: '+1234567890',
        },
        {
          amount: 300,
          status: 'test',
          payment_type: PaymentType.USER,
          service_type: ServiceType.IMMIGRATION,
          immigration_serviceId: 'test-immigration-id',
          userId: 'test-user-id-2',
        },
        {
          amount: 400,
          status: 'test',
          payment_type: PaymentType.GUEST,
          service_type: ServiceType.TRAINING,
          trainingId: 'test-training-id',
          guest_name: 'Test Guest 2',
          guest_email: '<EMAIL>',
          guest_mobile: '+1234567891',
        },
      ];

      const createdPayments = [];
      for (const paymentData of testPayments) {
        const payment = await prisma.payment.create({ data: paymentData });
        createdPayments.push(payment);
      }

      this.addResult(
        'Payment Creation',
        'PASS',
        `Successfully created ${createdPayments.length} test payments`,
        Date.now() - startTime,
      );

      // Clean up test data
      await prisma.payment.deleteMany({
        where: { status: 'test' },
      });
    } catch (error) {
      this.addResult(
        'Payment Creation',
        'FAIL',
        `Error creating payments: ${error.message}`,
        Date.now() - startTime,
      );
    }
  }

  private async testPaymentQueries(): Promise<void> {
    const startTime = Date.now();
    try {
      // Test various query patterns that the unified system supports
      const queries = [
        // Query by service type
        prisma.payment.findMany({
          where: { service_type: ServiceType.SERVICE },
          take: 5,
        }),

        // Query by payment type
        prisma.payment.findMany({
          where: { payment_type: PaymentType.USER },
          take: 5,
        }),

        // Query with date range
        prisma.payment.findMany({
          where: {
            createdAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
            },
          },
          take: 5,
        }),

        // Complex query with multiple filters
        prisma.payment.findMany({
          where: {
            AND: [
              { status: 'paid' },
              {
                service_type: {
                  in: [ServiceType.SERVICE, ServiceType.PACKAGE],
                },
              },
            ],
          },
          take: 5,
        }),
      ];

      await Promise.all(queries);

      this.addResult(
        'Payment Queries',
        'PASS',
        'All query patterns executed successfully',
        Date.now() - startTime,
      );
    } catch (error) {
      this.addResult(
        'Payment Queries',
        'FAIL',
        `Error executing queries: ${error.message}`,
        Date.now() - startTime,
      );
    }
  }

  private async testAnalytics(): Promise<void> {
    const startTime = Date.now();
    try {
      // Test analytics queries that demonstrate the power of unified table
      const analytics = await Promise.all([
        // Total revenue by service type
        prisma.payment.groupBy({
          by: ['service_type'],
          where: { status: 'paid' },
          _sum: { amount: true },
          _count: true,
        }),

        // Revenue by payment type
        prisma.payment.groupBy({
          by: ['payment_type'],
          where: { status: 'paid' },
          _sum: { amount: true },
          _count: true,
        }),

        // Monthly revenue trend
        prisma.$queryRaw`
          SELECT 
            DATE_TRUNC('month', "createdAt") as month,
            SUM(amount) as revenue,
            COUNT(*) as payment_count
          FROM payment 
          WHERE status = 'paid'
          GROUP BY DATE_TRUNC('month', "createdAt")
          ORDER BY month DESC
          LIMIT 12
        `,
      ]);

      this.addResult(
        'Analytics Queries',
        'PASS',
        'All analytics queries executed successfully',
        Date.now() - startTime,
      );
    } catch (error) {
      this.addResult(
        'Analytics Queries',
        'FAIL',
        `Error executing analytics: ${error.message}`,
        Date.now() - startTime,
      );
    }
  }

  private addResult(
    test: string,
    status: 'PASS' | 'FAIL',
    message: string,
    duration?: number,
  ): void {
    this.results.push({ test, status, message, duration });
  }

  private printResults(): void {
    console.log('\n📊 Test Results Summary\n');
    console.log('='.repeat(80));

    this.results.forEach((result) => {
      const statusIcon = result.status === 'PASS' ? '✅' : '❌';
      const durationText = result.duration ? ` (${result.duration}ms)` : '';
      console.log(`${statusIcon} ${result.test}${durationText}`);
      console.log(`   ${result.message}\n`);
    });

    const passCount = this.results.filter((r) => r.status === 'PASS').length;
    const totalCount = this.results.length;

    console.log('='.repeat(80));
    console.log(`📈 Overall Result: ${passCount}/${totalCount} tests passed`);

    if (passCount === totalCount) {
      console.log(
        '🎉 All tests passed! Unified payment system is ready for deployment.',
      );
    } else {
      console.log(
        '⚠️  Some tests failed. Please review and fix issues before deployment.',
      );
    }
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const tester = new UnifiedPaymentTester();
  tester
    .runAllTests()
    .then(() => {
      console.log('\n✨ Testing completed.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Testing failed:', error);
      process.exit(1);
    })
    .finally(() => {
      prisma.$disconnect();
    });
}

export { UnifiedPaymentTester };
