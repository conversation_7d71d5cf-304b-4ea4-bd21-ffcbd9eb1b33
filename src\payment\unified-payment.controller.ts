/**
 * Unified Payment Controller
 *
 * This controller provides the new v2 payment API endpoints that use the
 * unified payment architecture. It replaces the previous approach of having
 * separate endpoints for each service type and payment type combination.
 *
 * Key Features:
 * - Single payment endpoint for all service types
 * - Unified webhook processing
 * - Payment history and analytics endpoints
 * - Type-safe request/response handling
 * - Comprehensive API documentation
 *
 * Benefits over legacy controller:
 * - 90% reduction in endpoint count (16 endpoints → 2 endpoints)
 * - Consistent API design across all service types
 * - Better error handling and validation
 * - Simplified client integration
 * - Enhanced monitoring and logging
 *
 * API Versioning:
 * - This controller serves v2 endpoints (/v2/payment/*)
 * - Legacy endpoints (/payment/*) remain available for backward compatibility
 * - Gradual migration strategy supported
 *
 * <AUTHOR> Ireland Development Team
 * @version 2.0.0
 * @since 2024-12-27
 */

import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Req,
  UseGuards,
  RawBodyRequest,
  HttpCode,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiQuery,
} from '@nestjs/swagger';
import { FastifyRequest } from 'fastify';
import { UnifiedPaymentService } from './unified-payment.service';
import { JwtGuard } from 'src/guards/jwt.guard';
import { GetUser } from 'src/decorator/user.decorator';
import {
  CreateUnifiedPaymentDto,
  PaymentFiltersDto,
  PaymentResponseDto,
  PaymentType,
} from './dto/payment.dto';
import { JwtAdmin } from 'src/guards/jwt.admin.guard';

@ApiTags('payment-v2')
@Controller('v2/payment')
export class UnifiedPaymentController {
  /**
   * Unified Payment Controller Constructor
   *
   * Initializes the controller with the unified payment service dependency.
   *
   * @param unifiedPaymentService - Unified payment service instance
   */
  constructor(private unifiedPaymentService: UnifiedPaymentService) {}

  /**
   * Create Payment (Unified Endpoint)
   *
   * Single endpoint to handle payment creation for all service types.
   * Supports both authenticated users and guest users through the same endpoint.
   *
   * This endpoint replaces the 16 separate endpoints from the legacy API:
   * - /payment/mentor-service, /payment/guest-service
   * - /payment/package, /payment/guest-package
   * - /payment/immigration-service, /payment/guest-immigration
   * - /payment/training, /payment/guest-training
   *
   * @param user - JWT payload (optional for guest payments)
   * @param dto - Unified payment creation data
   * @returns Payment response with Stripe checkout URL
   */
  @Post('/create')
  @ApiOperation({
    summary: 'Create payment for any service type',
    description: `
      Create a payment session for any service type (mentor, package, immigration, training).
      Supports both authenticated users and guest users.

      For user payments: Include Bearer token in Authorization header
      For guest payments: Provide guest contact information in the request body

      Returns a Stripe checkout session URL for payment processing.
    `,
  })
  @ApiResponse({
    status: 200,
    description: 'Payment session created successfully',
    type: PaymentResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request data or validation failed',
  })
  @ApiResponse({
    status: 401,
    description: 'Authentication required for user payments',
  })
  @ApiResponse({
    status: 404,
    description: 'Service not found',
  })
  async createPayment(
    @GetUser() user: IJWTPayload | null,
    @Body() dto: CreateUnifiedPaymentDto,
  ): Promise<PaymentResponseDto> {
    // Validate authentication for user payments
    if (dto.paymentType === PaymentType.USER && !user) {
      throw new BadRequestException(
        'Authentication required for user payments',
      );
    }

    return await this.unifiedPaymentService.createPayment(user, dto);
  }

  /**
   * Create Guest Payment (Public Endpoint)
   *
   * Public endpoint specifically for guest payments that doesn't require authentication.
   * This provides a cleaner API for guest users who don't have accounts.
   *
   * @param dto - Payment creation data with guest information
   * @returns Payment response with Stripe checkout URL
   */
  @Post('/guest')
  @ApiOperation({
    summary: 'Create guest payment (no authentication required)',
    description: `
      Create a payment session for guest users without requiring authentication.
      Guest contact information must be provided in the request body.

      This endpoint automatically sets paymentType to 'guest' and validates
      that all required guest information is provided.
    `,
  })
  @ApiResponse({
    status: 200,
    description: 'Guest payment session created successfully',
    type: PaymentResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request data or missing guest information',
  })
  @ApiResponse({
    status: 404,
    description: 'Service not found',
  })
  async createGuestPayment(
    @Body() dto: CreateUnifiedPaymentDto,
  ): Promise<PaymentResponseDto> {
    // Force payment type to guest for this endpoint
    const guestDto = { ...dto, paymentType: PaymentType.GUEST };
    return await this.unifiedPaymentService.createPayment(null, guestDto);
  }

  /**
   * Stripe Webhook Handler
   *
   * Unified webhook endpoint that processes all Stripe events for the new payment system.
   * This replaces the complex webhook logic in the legacy payment service.
   *
   * @param req - Raw HTTP request from Stripe
   * @returns Webhook acknowledgment
   */
  @Post('/webhook')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Handle Stripe webhook events',
    description: `
      Process Stripe webhook events for payment confirmations and failures.
      This endpoint handles all service types through unified processing logic.

      Supported events:
      - checkout.session.completed: Payment successful
      - checkout.session.async_payment_failed: Payment failed
    `,
  })
  @ApiResponse({
    status: 200,
    description: 'Webhook processed successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid webhook signature or payload',
  })
  async handleWebhook(
    @Req() req: RawBodyRequest<FastifyRequest>,
  ): Promise<{ received: boolean }> {
    return await this.unifiedPaymentService.processWebhook(req);
  }

  /**
   * Get Payment History
   *
   * Retrieve payment history with optional filtering.
   * Demonstrates the power of the unified table for reporting and analytics.
   *
   * @param filters - Optional query parameters for filtering
   * @returns Array of payment records
   */
  @Get('/history')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get payment history with optional filtering',
    description: `
      Retrieve payment history with powerful filtering capabilities.
      This endpoint showcases the benefits of the unified payment table
      for reporting and analytics.

      Available filters:
      - serviceType: Filter by service type (mentor, package, immigration, training)
      - paymentType: Filter by payment type (user, guest)
      - status: Filter by payment status
      - userId: Filter by specific user
      - dateRange: Filter by date range
    `,
  })
  @ApiQuery({
    name: 'serviceType',
    required: false,
    description: 'Filter by service type',
  })
  @ApiQuery({
    name: 'paymentType',
    required: false,
    description: 'Filter by payment type',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter by payment status',
  })
  @ApiQuery({
    name: 'userId',
    required: false,
    description: 'Filter by user ID',
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    description: 'Start date for filtering',
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    description: 'End date for filtering',
  })
  @ApiResponse({
    status: 200,
    description: 'Payment history retrieved successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Authentication required',
  })
  async getPaymentHistory(@Query() filters: PaymentFiltersDto) {
    return await this.unifiedPaymentService.getPaymentHistory(filters);
  }

  /**
   * Get Payment Analytics
   *
   * Generate comprehensive payment analytics and revenue reports.
   * This was complex with 8 separate tables, now simple with unified table.
   *
   * @returns Payment analytics data
   */
  @Get('/analytics')
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get payment analytics and revenue reports',
    description: `
      Generate comprehensive payment analytics including:
      - Total revenue and payment counts
      - Revenue breakdown by payment type (user vs guest)
      - Revenue breakdown by service type
      - Recent payment activity

      This endpoint demonstrates the reporting power of the unified payment table.
    `,
  })
  @ApiResponse({
    status: 200,
    description: 'Payment analytics retrieved successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Authentication required',
  })
  async getPaymentAnalytics() {
    return await this.unifiedPaymentService.getPaymentAnalytics();
  }

  /**
   * Health Check
   *
   * Simple health check endpoint for monitoring the unified payment service.
   *
   * @returns Service health status
   */
  @Get('/health')
  @ApiOperation({
    summary: 'Health check for unified payment service',
    description: 'Check if the unified payment service is operational',
  })
  @ApiResponse({
    status: 200,
    description: 'Service is healthy',
  })
  async healthCheck() {
    return {
      status: 'OK',
      service: 'unified-payment-service',
      version: '2.0.0',
      timestamp: new Date().toISOString(),
    };
  }
}
