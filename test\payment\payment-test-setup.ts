/**
 * Payment Test Setup
 * 
 * Specialized setup for payment-related tests including database
 * initialization, mock configurations, and test utilities.
 * 
 * Features:
 * - Database connection management
 * - Stripe mock setup
 * - Email service mocking
 * - Performance monitoring
 * - Test data management
 */

import { DatabaseTestUtils } from '../utils/database-test-utils';
import { PaymentFixtures } from '../fixtures/payment-fixtures';

// Extend Jest matchers for payment testing
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidPayment(): R;
      toHaveValidStripeSession(): R;
      toBeCompletedPayment(): R;
      toHavePendingStatus(): R;
      toMatchPaymentAmount(expected: number): R;
    }
  }
}

// Custom Jest matchers for payment testing
expect.extend({
  /**
   * Validates that an object is a valid payment record
   */
  toBeValidPayment(received: any) {
    const requiredFields = [
      'id', 'serviceType', 'serviceId', 'paymentType', 
      'amount', 'currency', 'status', 'createdAt'
    ];
    
    const missingFields = requiredFields.filter(field => 
      received[field] === undefined || received[field] === null
    );
    
    if (missingFields.length > 0) {
      return {
        message: () => `Payment is missing required fields: ${missingFields.join(', ')}`,
        pass: false,
      };
    }
    
    // Validate amount is positive
    if (received.amount <= 0) {
      return {
        message: () => `Payment amount must be positive, got: ${received.amount}`,
        pass: false,
      };
    }
    
    // Validate service type
    const validServiceTypes = ['service', 'package', 'immigration', 'training'];
    if (!validServiceTypes.includes(received.serviceType)) {
      return {
        message: () => `Invalid service type: ${received.serviceType}`,
        pass: false,
      };
    }
    
    // Validate payment type
    const validPaymentTypes = ['user', 'guest'];
    if (!validPaymentTypes.includes(received.paymentType)) {
      return {
        message: () => `Invalid payment type: ${received.paymentType}`,
        pass: false,
      };
    }
    
    return {
      message: () => 'Payment is valid',
      pass: true,
    };
  },

  /**
   * Validates that an object has a valid Stripe session structure
   */
  toHaveValidStripeSession(received: any) {
    const requiredFields = ['id', 'url'];
    const missingFields = requiredFields.filter(field => !received[field]);
    
    if (missingFields.length > 0) {
      return {
        message: () => `Stripe session is missing required fields: ${missingFields.join(', ')}`,
        pass: false,
      };
    }
    
    // Validate URL format
    if (!received.url.includes('checkout.stripe.com')) {
      return {
        message: () => `Invalid Stripe checkout URL: ${received.url}`,
        pass: false,
      };
    }
    
    return {
      message: () => 'Stripe session is valid',
      pass: true,
    };
  },

  /**
   * Validates that a payment is in completed status
   */
  toBeCompletedPayment(received: any) {
    if (received.status !== 'completed') {
      return {
        message: () => `Expected payment to be completed, got: ${received.status}`,
        pass: false,
      };
    }
    
    if (!received.completedAt) {
      return {
        message: () => 'Completed payment must have completedAt timestamp',
        pass: false,
      };
    }
    
    return {
      message: () => 'Payment is completed',
      pass: true,
    };
  },

  /**
   * Validates that a payment has pending status
   */
  toHavePendingStatus(received: any) {
    if (received.status !== 'pending') {
      return {
        message: () => `Expected payment to be pending, got: ${received.status}`,
        pass: false,
      };
    }
    
    if (received.completedAt) {
      return {
        message: () => 'Pending payment should not have completedAt timestamp',
        pass: false,
      };
    }
    
    return {
      message: () => 'Payment is pending',
      pass: true,
    };
  },

  /**
   * Validates that a payment amount matches expected value
   */
  toMatchPaymentAmount(received: any, expected: number) {
    if (received.amount !== expected) {
      return {
        message: () => `Expected payment amount ${expected}, got: ${received.amount}`,
        pass: false,
      };
    }
    
    return {
      message: () => `Payment amount matches expected value: ${expected}`,
      pass: true,
    };
  },
});

// Global test setup for payment tests
beforeAll(async () => {
  // Initialize database connection
  await DatabaseTestUtils.initialize();
  
  // Set up environment variables for testing
  process.env.STRIPE_SECRET_KEY = PaymentFixtures.STRIPE_WEBHOOK_SECRET;
  process.env.STRIPE_WEBHOOK_SECRET = PaymentFixtures.STRIPE_WEBHOOK_SECRET;
  
  console.log('🔧 Payment test environment initialized');
});

// Setup before each test
beforeEach(async () => {
  // Clear and seed test data
  await DatabaseTestUtils.clearPaymentData();
  await DatabaseTestUtils.seedPaymentTestData();
  
  // Reset all mocks
  jest.clearAllMocks();
  
  // Setup default mock implementations
  setupDefaultMocks();
});

// Cleanup after all tests
afterAll(async () => {
  // Clean up database
  await DatabaseTestUtils.clearPaymentData();
  await DatabaseTestUtils.cleanup();
  
  console.log('🧹 Payment test environment cleaned up');
});

/**
 * Setup default mock implementations for external services
 */
function setupDefaultMocks() {
  // Mock Stripe
  const mockStripe = {
    checkout: {
      sessions: {
        create: jest.fn().mockResolvedValue(PaymentFixtures.getMockStripeSession()),
        retrieve: jest.fn().mockResolvedValue(PaymentFixtures.getMockStripeSession()),
      },
    },
    webhooks: {
      constructEvent: jest.fn().mockReturnValue(PaymentFixtures.getMockStripeWebhookEvent()),
    },
  };

  // Mock Mailer Service
  const mockMailer = {
    sendPaymentConfirmation: jest.fn().mockResolvedValue(true),
    sendAdminNotification: jest.fn().mockResolvedValue(true),
    sendPaymentFailure: jest.fn().mockResolvedValue(true),
  };

  // Mock JWT Service
  const mockJwtService = {
    sign: jest.fn().mockReturnValue('mock-jwt-token'),
    verify: jest.fn().mockReturnValue({
      id: PaymentFixtures.TEST_USER_ID,
      email: '<EMAIL>',
      name: 'Test User',
    }),
  };

  // Store mocks globally for test access
  global.mockStripe = mockStripe;
  global.mockMailer = mockMailer;
  global.mockJwtService = mockJwtService;
}

/**
 * Test utilities for payment testing
 */
export class PaymentTestUtils {
  /**
   * Create a test payment and return its ID
   */
  static async createTestPayment(overrides = {}) {
    const prisma = await DatabaseTestUtils.initialize();
    const paymentData = {
      ...PaymentFixtures.getMockUserPayment(),
      ...overrides,
    };
    
    const payment = await prisma.payment.create({ data: paymentData });
    return payment.id;
  }

  /**
   * Simulate a successful Stripe webhook
   */
  static createSuccessfulWebhookEvent(paymentId: string) {
    const event = PaymentFixtures.getMockStripeWebhookEvent();
    event.data.object.metadata.paymentId = paymentId;
    event.type = 'checkout.session.completed';
    return event;
  }

  /**
   * Simulate a failed Stripe webhook
   */
  static createFailedWebhookEvent(paymentId: string) {
    const event = PaymentFixtures.getMockStripeWebhookEvent();
    event.data.object.metadata.paymentId = paymentId;
    event.type = 'checkout.session.async_payment_failed';
    return event;
  }

  /**
   * Wait for async operations to complete
   */
  static async waitForAsyncOperations(ms = 100) {
    await new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Verify payment state in database
   */
  static async verifyPaymentState(paymentId: string, expectedState: Partial<any>) {
    const prisma = await DatabaseTestUtils.initialize();
    const payment = await prisma.payment.findUnique({
      where: { id: paymentId },
    });
    
    expect(payment).toBeDefined();
    
    for (const [key, value] of Object.entries(expectedState)) {
      expect(payment[key]).toBe(value);
    }
    
    return payment;
  }

  /**
   * Generate performance test data
   */
  static async generatePerformanceTestData(count: number) {
    const payments = Array.from({ length: count }, (_, index) => ({
      ...PaymentFixtures.getMockUserPayment(),
      id: `perf-test-${index}`,
      stripeSessionId: `cs_perf_${index}`,
      amount: Math.floor(Math.random() * 50000) + 1000,
    }));

    const prisma = await DatabaseTestUtils.initialize();
    await prisma.payment.createMany({
      data: payments,
      skipDuplicates: true,
    });

    return payments.map(p => p.id);
  }

  /**
   * Clean up performance test data
   */
  static async cleanupPerformanceTestData() {
    const prisma = await DatabaseTestUtils.initialize();
    await prisma.payment.deleteMany({
      where: {
        id: { startsWith: 'perf-test-' },
      },
    });
  }

  /**
   * Measure test execution time
   */
  static async measureExecutionTime<T>(
    testFn: () => Promise<T>
  ): Promise<{ result: T; executionTime: number }> {
    const startTime = performance.now();
    const result = await testFn();
    const endTime = performance.now();
    
    return {
      result,
      executionTime: endTime - startTime,
    };
  }

  /**
   * Create mock HTTP request for webhook testing
   */
  static createMockWebhookRequest(event: any, signature = 'test-signature') {
    return {
      body: Buffer.from(JSON.stringify(event)),
      headers: {
        'stripe-signature': signature,
      },
    };
  }

  /**
   * Validate test environment
   */
  static validateTestEnvironment() {
    const requiredEnvVars = [
      'DATABASE_URL',
      'STRIPE_SECRET_KEY',
      'STRIPE_WEBHOOK_SECRET',
    ];

    const missingVars = requiredEnvVars.filter(
      varName => !process.env[varName]
    );

    if (missingVars.length > 0) {
      throw new Error(
        `Missing required environment variables: ${missingVars.join(', ')}`
      );
    }
  }
}

// Validate test environment on setup
PaymentTestUtils.validateTestEnvironment();
