/**
 * Payment Test Fixtures
 *
 * Centralized test data fixtures for payment-related tests.
 * Provides consistent, realistic test data across all payment test suites.
 *
 * Features:
 * - Realistic payment scenarios
 * - Multiple service types
 * - User and guest payment flows
 * - Error scenarios
 * - Edge cases
 */

import {
  ServiceType,
  PaymentType,
  CreateUnifiedPaymentDto,
} from '../../src/payment/dto/payment.dto';

export class PaymentFixtures {
  // Base test data
  static readonly TEST_USER_ID = 'test-user-123';
  static readonly TEST_SERVICE_ID = 'test-service-456';
  static readonly TEST_PACKAGE_ID = 'test-package-789';
  static readonly TEST_IMMIGRATION_ID = 'test-immigration-101';
  static readonly TEST_TRAINING_ID = 'test-training-202';

  // Stripe test data
  static readonly STRIPE_SESSION_ID = 'cs_test_session_123';
  static readonly STRIPE_PAYMENT_INTENT_ID = 'pi_test_intent_456';
  static readonly STRIPE_WEBHOOK_SECRET = 'whsec_test_secret';

  /**
   * Valid payment DTOs for different scenarios
   */
  static getValidUserServicePayment(): CreateUnifiedPaymentDto {
    return {
      serviceType: ServiceType.SERVICE,
      serviceId: this.TEST_SERVICE_ID,
      paymentType: PaymentType.USER,
    };
  }

  static getValidGuestPackagePayment(): CreateUnifiedPaymentDto {
    return {
      serviceType: ServiceType.PACKAGE,
      serviceId: this.TEST_PACKAGE_ID,
      paymentType: PaymentType.GUEST,
      name: 'John Doe',
      email: '<EMAIL>',
      mobile: '+353871234567',
    };
  }

  static getValidUserImmigrationPayment(): CreateUnifiedPaymentDto {
    return {
      serviceType: ServiceType.IMMIGRATION,
      serviceId: this.TEST_IMMIGRATION_ID,
      paymentType: PaymentType.USER,
    };
  }

  static getValidGuestTrainingPayment(): CreateUnifiedPaymentDto {
    return {
      serviceType: ServiceType.TRAINING,
      serviceId: this.TEST_TRAINING_ID,
      paymentType: PaymentType.GUEST,
      name: 'Jane Smith',
      email: '<EMAIL>',
      mobile: '+353879876543',
    };
  }

  /**
   * Invalid payment DTOs for error testing
   */
  static getInvalidPaymentMissingServiceId(): Partial<CreateUnifiedPaymentDto> {
    return {
      serviceType: ServiceType.SERVICE,
      paymentType: PaymentType.USER,
      // Missing serviceId
    };
  }

  static getInvalidGuestPaymentMissingInfo(): CreateUnifiedPaymentDto {
    return {
      serviceType: ServiceType.PACKAGE,
      serviceId: this.TEST_PACKAGE_ID,
      paymentType: PaymentType.GUEST,
      // Missing name, email, mobile
    };
  }

  static getInvalidServiceType(): any {
    return {
      serviceType: 'invalid-service-type',
      serviceId: this.TEST_SERVICE_ID,
      paymentType: PaymentType.USER,
    };
  }

  /**
   * Mock service data
   */
  static getMockService() {
    return {
      id: this.TEST_SERVICE_ID,
      name: 'Career Consultation',
      amount: 10000, // €100.00 in cents
      desc: 'Professional career guidance session',
      mentorId: 'mentor-123',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
    };
  }

  static getMockPackage() {
    return {
      id: this.TEST_PACKAGE_ID,
      name: 'Complete Career Package',
      amount: 25000, // €250.00 in cents
      desc: 'Comprehensive career development package',
      service: ['CV Review', 'Interview Prep', 'Career Planning'],
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
    };
  }

  static getMockImmigrationService() {
    return {
      id: this.TEST_IMMIGRATION_ID,
      name: 'Visa Application Support',
      amount: 30000, // €300.00 in cents
      service: ['Document Review', 'Application Assistance', 'Interview Prep'],
      order: 1,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
    };
  }

  static getMockTraining() {
    return {
      id: this.TEST_TRAINING_ID,
      name: 'Technical Skills Training',
      amount: 50000, // €500.00 in cents
      desc: 'Advanced technical skills development program',
      order: 1,
      image: 'training-image.jpg',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
    };
  }

  /**
   * Mock payment records
   */
  static getMockUserPayment() {
    return {
      id: 'payment-user-123',
      service_type: ServiceType.SERVICE,
      serviceId: this.TEST_SERVICE_ID,
      payment_type: PaymentType.USER,
      userId: this.TEST_USER_ID,
      amount: 10000,
      status: 'pending',
      stripe_session_id: this.STRIPE_SESSION_ID,
      stripe_payment_intent_id: null,
      guest_name: null,
      guest_email: null,
      guest_mobile: null,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
    };
  }

  static getMockGuestPayment() {
    return {
      id: 'payment-guest-456',
      service_type: ServiceType.PACKAGE,
      packageId: this.TEST_PACKAGE_ID,
      payment_type: PaymentType.GUEST,
      userId: null,
      amount: 25000,
      status: 'pending',
      stripe_session_id: this.STRIPE_SESSION_ID,
      stripe_payment_intent_id: null,
      guest_name: 'John Doe',
      guest_email: '<EMAIL>',
      guest_mobile: '+353871234567',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
    };
  }

  static getMockCompletedPayment() {
    return {
      ...this.getMockUserPayment(),
      id: 'payment-completed-789',
      status: 'completed',
      stripe_payment_intent_id: this.STRIPE_PAYMENT_INTENT_ID,
    };
  }

  /**
   * Stripe mock responses
   */
  static getMockStripeSession() {
    return {
      id: this.STRIPE_SESSION_ID,
      url: 'https://checkout.stripe.com/c/pay/test_session',
      payment_intent: this.STRIPE_PAYMENT_INTENT_ID,
      customer_email: '<EMAIL>',
      amount_total: 10000,
      currency: 'eur',
      payment_status: 'paid',
      metadata: {
        paymentId: 'payment-user-123',
        serviceType: ServiceType.SERVICE,
        serviceId: this.TEST_SERVICE_ID,
      },
    };
  }

  static getMockStripeWebhookEvent() {
    return {
      id: 'evt_test_webhook',
      type: 'checkout.session.completed',
      data: {
        object: this.getMockStripeSession(),
      },
      created: Math.floor(Date.now() / 1000),
      livemode: false,
      pending_webhooks: 1,
      request: {
        id: 'req_test_request',
        idempotency_key: null,
      },
    };
  }

  /**
   * Payment analytics mock data
   */
  static getMockPaymentAnalytics() {
    return {
      totalRevenue: 150000, // €1,500.00
      totalPayments: 15,
      revenueByPaymentType: [
        {
          paymentType: 'user',
          _sum: { amount: 120000 },
          _count: 12,
        },
        {
          paymentType: 'guest',
          _sum: { amount: 30000 },
          _count: 3,
        },
      ],
      revenueByServiceType: [
        {
          serviceType: 'service',
          _sum: { amount: 50000 },
          _count: 5,
        },
        {
          serviceType: 'package',
          _sum: { amount: 75000 },
          _count: 3,
        },
        {
          serviceType: 'immigration',
          _sum: { amount: 15000 },
          _count: 5,
        },
        {
          serviceType: 'training',
          _sum: { amount: 10000 },
          _count: 2,
        },
      ],
    };
  }

  /**
   * Edge case scenarios
   */
  static getLargeAmountPayment(): CreateUnifiedPaymentDto {
    return {
      serviceType: ServiceType.TRAINING,
      serviceId: this.TEST_TRAINING_ID,
      paymentType: PaymentType.USER,
    };
  }

  static getMinimumAmountPayment(): CreateUnifiedPaymentDto {
    return {
      serviceType: ServiceType.SERVICE,
      serviceId: this.TEST_SERVICE_ID,
      paymentType: PaymentType.USER,
    };
  }

  /**
   * Performance test data
   */
  static generateBulkPayments(count: number) {
    return Array.from({ length: count }, (_, index) => ({
      ...this.getMockUserPayment(),
      id: `payment-bulk-${index}`,
      amount: Math.floor(Math.random() * 50000) + 1000, // Random amount between €10-€500
      createdAt: new Date(
        Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000,
      ), // Random date within last 30 days
    }));
  }

  /**
   * Database constraint test data
   */
  static getPaymentWithInvalidForeignKey() {
    return {
      ...this.getMockUserPayment(),
      serviceId: 'non-existent-service-id',
    };
  }

  static getPaymentWithInvalidUserId() {
    return {
      ...this.getMockUserPayment(),
      userId: 'non-existent-user-id',
    };
  }
}
