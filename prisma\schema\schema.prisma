generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["prismaSchemaFolder"]
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

enum Day {
  Monday
  Tuesday
  Wednesday
  Thursday
  Friday
  Saturday
  Sunday
}

enum Status {
  Accepted
  Rejected
  Pending
  Completed
  Active
  Inactive
  Blocked
  Cancelled
  Refunded
}

enum Provider {
  credentials
  google
}
