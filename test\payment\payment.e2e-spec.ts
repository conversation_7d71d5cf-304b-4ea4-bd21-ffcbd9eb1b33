/**
 * Payment End-to-End Tests
 *
 * Complete end-to-end tests for the unified payment system API.
 * Tests the entire payment workflow from HTTP requests through
 * to database persistence and external service integration.
 *
 * Test Categories:
 * - Complete payment API workflows
 * - Authentication and authorization
 * - Input validation and error responses
 * - Webhook endpoint testing
 * - Payment analytics API
 * - Cross-service integration
 * - Real-world scenario simulation
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as request from 'supertest';
import { PrismaClient } from '@prisma/client';
import { AppModule } from '../../src/app.module';
import { PaymentFixtures } from '../fixtures/payment-fixtures';
import { DatabaseTestUtils } from '../utils/database-test-utils';
import { ServiceType, PaymentType } from '../../src/payment/dto/payment.dto';

describe('Payment E2E Tests', () => {
  let app: INestApplication;
  let prisma: PrismaClient;
  let jwtService: JwtService;
  let authToken: string;
  let adminToken: string;

  beforeAll(async () => {
    // Initialize test database
    prisma = await DatabaseTestUtils.initialize();

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider('STRIPE_CLIENT')
      .useValue({
        checkout: {
          sessions: {
            create: jest
              .fn()
              .mockResolvedValue(PaymentFixtures.getMockStripeSession()),
            retrieve: jest
              .fn()
              .mockResolvedValue(PaymentFixtures.getMockStripeSession()),
          },
        },
        webhooks: {
          constructEvent: jest
            .fn()
            .mockReturnValue(PaymentFixtures.getMockStripeWebhookEvent()),
        },
      })
      .compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    jwtService = moduleFixture.get<JwtService>(JwtService);

    // Generate test tokens
    authToken = jwtService.sign({
      id: PaymentFixtures.TEST_USER_ID,
      email: '<EMAIL>',
      name: 'Test User',
    });

    adminToken = jwtService.sign({
      id: 'admin-user-id',
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'admin',
    });
  });

  beforeEach(async () => {
    // Clear and seed test data before each test
    await DatabaseTestUtils.clearPaymentData();
    await DatabaseTestUtils.seedPaymentTestData();
  });

  afterAll(async () => {
    await DatabaseTestUtils.cleanup();
    await app.close();
  });

  describe('POST /v2/payment/create', () => {
    /**
     * Test: Authenticated User Service Payment
     * Purpose: Verify complete user payment creation workflow via API
     * Expected: 200 response with payment URL and database record creation
     */
    it('should create user service payment successfully', async () => {
      // Arrange
      const paymentDto = PaymentFixtures.getValidUserServicePayment();

      // Act
      const response = await request(app.getHttpServer())
        .post('/v2/payment/create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(paymentDto)
        .expect(200);

      // Assert API response
      expect(response.body).toEqual({
        status: 'success',
        url: expect.stringContaining('checkout.stripe.com'),
        paymentId: expect.any(String),
      });

      // Verify database record
      const payment = await prisma.payment.findUnique({
        where: { id: response.body.paymentId },
      });
      expect(payment).toBeDefined();
      expect(payment.service_type).toBe(ServiceType.SERVICE);
      expect(payment.payment_type).toBe(PaymentType.USER);
      expect(payment.userId).toBe(PaymentFixtures.TEST_USER_ID);
      expect(payment.amount).toBe(10000); // From mock service
    });

    /**
     * Test: Input Validation - Missing Required Fields
     * Purpose: Verify API validates required fields and returns appropriate errors
     * Expected: 400 response with validation error details
     */
    it('should return 400 for missing required fields', async () => {
      // Arrange
      const invalidDto = PaymentFixtures.getInvalidPaymentMissingServiceId();

      // Act & Assert
      const response = await request(app.getHttpServer())
        .post('/v2/payment/create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidDto)
        .expect(400);

      expect(response.body.message).toContain('serviceId');
    });

    /**
     * Test: Authentication Required for User Payments
     * Purpose: Verify user payments require valid authentication
     * Expected: 401 response for missing/invalid token
     */
    it('should return 401 for user payment without authentication', async () => {
      // Arrange
      const paymentDto = PaymentFixtures.getValidUserServicePayment();

      // Act & Assert
      await request(app.getHttpServer())
        .post('/v2/payment/create')
        .send(paymentDto)
        .expect(401);
    });

    /**
     * Test: Service Not Found Error
     * Purpose: Verify proper error handling for non-existent services
     * Expected: 404 response with appropriate error message
     */
    it('should return 404 for non-existent service', async () => {
      // Arrange
      const paymentDto = {
        ...PaymentFixtures.getValidUserServicePayment(),
        serviceId: 'non-existent-service-id',
      };

      // Act & Assert
      const response = await request(app.getHttpServer())
        .post('/v2/payment/create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(paymentDto)
        .expect(404);

      expect(response.body.message).toContain('Service not found');
    });

    /**
     * Test: Multiple Service Types Support
     * Purpose: Verify API supports all service types (service, package, immigration, training)
     * Expected: Successful payment creation for each service type
     */
    it('should support all service types', async () => {
      const testCases = [
        {
          dto: PaymentFixtures.getValidUserServicePayment(),
          expectedType: ServiceType.SERVICE,
        },
        {
          dto: {
            serviceType: ServiceType.PACKAGE,
            serviceId: PaymentFixtures.TEST_PACKAGE_ID,
            paymentType: PaymentType.USER,
          },
          expectedType: ServiceType.PACKAGE,
        },
        {
          dto: PaymentFixtures.getValidUserImmigrationPayment(),
          expectedType: ServiceType.IMMIGRATION,
        },
        {
          dto: {
            serviceType: ServiceType.TRAINING,
            serviceId: PaymentFixtures.TEST_TRAINING_ID,
            paymentType: PaymentType.USER,
          },
          expectedType: ServiceType.TRAINING,
        },
      ];

      for (const testCase of testCases) {
        const response = await request(app.getHttpServer())
          .post('/v2/payment/create')
          .set('Authorization', `Bearer ${authToken}`)
          .send(testCase.dto)
          .expect(200);

        expect(response.body.status).toBe('success');

        const payment = await prisma.payment.findUnique({
          where: { id: response.body.paymentId },
        });
        expect(payment.service_type).toBe(testCase.expectedType);
      }
    });
  });

  describe('POST /v2/payment/guest', () => {
    /**
     * Test: Guest Payment Creation
     * Purpose: Verify guest payment creation without authentication
     * Expected: 200 response with payment URL and guest information stored
     */
    it('should create guest payment successfully', async () => {
      // Arrange
      const guestDto = PaymentFixtures.getValidGuestPackagePayment();

      // Act
      const response = await request(app.getHttpServer())
        .post('/v2/payment/guest')
        .send(guestDto)
        .expect(200);

      // Assert API response
      expect(response.body).toEqual({
        status: 'success',
        url: expect.stringContaining('checkout.stripe.com'),
        paymentId: expect.any(String),
      });

      // Verify database record with guest information
      const payment = await prisma.payment.findUnique({
        where: { id: response.body.paymentId },
      });
      expect(payment).toBeDefined();
      expect(payment.payment_type).toBe(PaymentType.GUEST);
      expect(payment.userId).toBeNull();
      expect(payment.guest_name).toBe('John Doe');
      expect(payment.guest_email).toBe('<EMAIL>');
      expect(payment.guest_mobile).toBe('+353871234567');
    });

    /**
     * Test: Guest Payment Missing Contact Information
     * Purpose: Verify guest payments require complete contact information
     * Expected: 400 response for missing guest details
     */
    it('should return 400 for missing guest information', async () => {
      // Arrange
      const incompleteDto = PaymentFixtures.getInvalidGuestPaymentMissingInfo();

      // Act & Assert
      const response = await request(app.getHttpServer())
        .post('/v2/payment/guest')
        .send(incompleteDto)
        .expect(400);

      expect(response.body.message).toContain('Guest information required');
    });
  });

  describe('POST /v2/payment/webhook', () => {
    /**
     * Test: Successful Payment Webhook Processing
     * Purpose: Verify webhook processes payment completion correctly
     * Expected: 200 response and payment status updated to completed
     */
    it('should process successful payment webhook', async () => {
      // Arrange - Create a pending payment first
      const paymentDto = PaymentFixtures.getValidUserServicePayment();
      const createResponse = await request(app.getHttpServer())
        .post('/v2/payment/create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(paymentDto);

      const paymentId = createResponse.body.paymentId;

      // Prepare webhook payload
      const webhookEvent = PaymentFixtures.getMockStripeWebhookEvent();
      webhookEvent.data.object.metadata.paymentId = paymentId;

      // Act
      const response = await request(app.getHttpServer())
        .post('/v2/payment/webhook')
        .set('stripe-signature', 'test-signature')
        .send(webhookEvent)
        .expect(200);

      // Assert
      expect(response.body).toEqual({ received: true });

      // Verify payment status updated
      const updatedPayment = await prisma.payment.findUnique({
        where: { id: paymentId },
      });
      expect(updatedPayment.status).toBe('completed');
    });

    /**
     * Test: Webhook Signature Validation
     * Purpose: Verify webhook rejects requests with invalid signatures
     * Expected: 400 response for invalid signature
     */
    it('should reject webhook with invalid signature', async () => {
      // Arrange
      const webhookEvent = PaymentFixtures.getMockStripeWebhookEvent();

      // Act & Assert
      await request(app.getHttpServer())
        .post('/v2/payment/webhook')
        .set('stripe-signature', 'invalid-signature')
        .send(webhookEvent)
        .expect(400);
    });
  });

  describe('GET /v2/payment/history', () => {
    /**
     * Test: Payment History Retrieval with Admin Access
     * Purpose: Verify admin can access payment history with filtering
     * Expected: 200 response with filtered payment data
     */
    it('should return payment history for admin users', async () => {
      // Arrange - Create test payments
      await request(app.getHttpServer())
        .post('/v2/payment/create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(PaymentFixtures.getValidUserServicePayment());

      await request(app.getHttpServer())
        .post('/v2/payment/guest')
        .send(PaymentFixtures.getValidGuestPackagePayment());

      // Act
      const response = await request(app.getHttpServer())
        .get('/v2/payment/history')
        .set('Authorization', `Bearer ${adminToken}`)
        .query({ paymentType: PaymentType.USER })
        .expect(200);

      // Assert
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(
        response.body.every((p: any) => p.payment_type === PaymentType.USER),
      ).toBe(true);
    });

    /**
     * Test: Unauthorized Access to Payment History
     * Purpose: Verify non-admin users cannot access payment history
     * Expected: 401/403 response for unauthorized access
     */
    it('should deny access to non-admin users', async () => {
      // Act & Assert
      await request(app.getHttpServer())
        .get('/v2/payment/history')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(401);
    });
  });

  describe('GET /v2/payment/analytics', () => {
    /**
     * Test: Payment Analytics Generation
     * Purpose: Verify analytics endpoint returns comprehensive payment data
     * Expected: 200 response with revenue and payment statistics
     */
    it('should return payment analytics for admin users', async () => {
      // Arrange - Create test payments
      await request(app.getHttpServer())
        .post('/v2/payment/create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(PaymentFixtures.getValidUserServicePayment());

      // Act
      const response = await request(app.getHttpServer())
        .get('/v2/payment/analytics')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      // Assert
      expect(response.body).toEqual({
        totalRevenue: expect.any(Number),
        totalPayments: expect.any(Number),
        revenueByPaymentType: expect.any(Array),
        revenueByServiceType: expect.any(Array),
      });
    });
  });

  describe('GET /v2/payment/health', () => {
    /**
     * Test: Health Check Endpoint
     * Purpose: Verify service health monitoring endpoint
     * Expected: 200 response with service status
     */
    it('should return service health status', async () => {
      // Act
      const response = await request(app.getHttpServer())
        .get('/v2/payment/health')
        .expect(200);

      // Assert
      expect(response.body).toEqual({
        status: 'OK',
        service: 'unified-payment-service',
        version: '2.0.0',
        timestamp: expect.any(String),
      });
    });
  });

  describe('Real-world Scenario Simulation', () => {
    /**
     * Test: Complete User Journey
     * Purpose: Simulate a complete user payment journey from creation to completion
     * Expected: All steps complete successfully with proper data persistence
     */
    it('should handle complete user payment journey', async () => {
      // Step 1: User creates payment
      const createResponse = await request(app.getHttpServer())
        .post('/v2/payment/create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(PaymentFixtures.getValidUserServicePayment())
        .expect(200);

      const paymentId = createResponse.body.paymentId;

      // Step 2: Verify payment is pending
      let payment = await prisma.payment.findUnique({
        where: { id: paymentId },
      });
      expect(payment.status).toBe('pending');

      // Step 3: Simulate successful payment webhook
      const webhookEvent = PaymentFixtures.getMockStripeWebhookEvent();
      webhookEvent.data.object.metadata.paymentId = paymentId;

      await request(app.getHttpServer())
        .post('/v2/payment/webhook')
        .set('stripe-signature', 'test-signature')
        .send(webhookEvent)
        .expect(200);

      // Step 4: Verify payment is completed
      payment = await prisma.payment.findUnique({
        where: { id: paymentId },
      });
      expect(payment.status).toBe('completed');

      // Step 5: Verify payment appears in analytics
      const analyticsResponse = await request(app.getHttpServer())
        .get('/v2/payment/analytics')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(analyticsResponse.body.totalPayments).toBeGreaterThan(0);
      expect(analyticsResponse.body.totalRevenue).toBeGreaterThan(0);
    });
  });
});
