{"name": "api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "prod:build": "npx prisma generate && npx prisma migrate deploy && nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:payment": "jest --testPathPattern=payment", "test:payment:unit": "jest --testPathPattern=payment.*\\.spec\\.ts$", "test:payment:integration": "jest --testPathPattern=payment.*\\.integration\\.spec\\.ts$", "test:payment:e2e": "jest --testPathPattern=payment.*\\.e2e-spec\\.ts$", "test:db": "jest --testPathPattern=database", "migrate:dev": "prisma migrate dev", "db:push": "prisma db push", "migrate:reset": "prisma migrate reset", "db:seed": "prisma db seed", "prisma:generate": "prisma generate", "prisma:studio": "prisma studio", "migrate:payments": "ts-node scripts/migrate-payments.ts", "validate:payment-migration": "ts-node scripts/validate-payment-migration.ts", "rollback:payment-migration": "ts-node scripts/rollback-payment-migration.ts", "seed:payment-data": "ts-node scripts/insert-payment-test-data.ts", "cleanup:payment-data": "ts-node scripts/cleanup-payment-test-data.ts", "create:sample-data": "ts-node scripts/create-sample-data.ts"}, "dependencies": {"@fastify/multipart": "^8.2.0", "@fastify/static": "^7.0.2", "@nest-lab/fastify-multer": "^1.2.0", "@nestjs/class-transformer": "^0.4.0", "@nestjs/class-validator": "^0.13.4", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.1", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "^2.0.5", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-fastify": "^10.3.7", "@nestjs/swagger": "^7.3.1", "@prisma/client": "^6.5.0", "@react-email/components": "^0.0.32", "@supabase/supabase-js": "^2.43.1", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "date-fns": "^4.1.0", "mammoth": "^1.7.2", "nodemailer": "^6.9.13", "openai": "^4.47.1", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "pdfmake": "^0.2.10", "pdfreader": "^3.0.2", "reflect-metadata": "^0.2.0", "resend": "^4.1.1", "rxjs": "^7.8.1", "stripe": "^17.4.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.2", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.11", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.14", "@types/react": "^19.0.7", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "prisma": "^6.5.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "roots": ["<rootDir>/src", "<rootDir>/test"], "testRegex": ".*\\.(spec|e2e-spec)\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s", "!**/*.module.ts", "!**/*.interface.ts", "!**/*.dto.ts", "!**/main.ts"], "coverageDirectory": "../coverage", "testEnvironment": "node", "coverageReporters": ["text", "lcov", "html"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}, "setupFilesAfterEnv": ["<rootDir>/test/setup.ts"], "moduleNameMapper": {"^src/(.*)$": "<rootDir>/src/$1"}}, "resolutions": {"string-width": "4.2.3"}}