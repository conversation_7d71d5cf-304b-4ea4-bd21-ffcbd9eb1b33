/**
 * Simple Payment E2E Tests
 *
 * Simplified end-to-end tests for the payment system that focus on
 * testing the core payment functionality without complex dependencies.
 * This helps isolate and fix basic issues before running full integration tests.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as request from 'supertest';
import { PaymentModule } from '../../src/payment/payment.module';
import { PaymentFixtures } from '../fixtures/payment-fixtures';
import { ServiceType, PaymentType } from '../../src/payment/dto/payment.dto';

describe('Payment Simple E2E Tests', () => {
  let app: INestApplication;
  let jwtService: JwtService;
  let authToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [PaymentModule],
    })
      .overrideProvider('STRIPE_CLIENT')
      .useValue({
        checkout: {
          sessions: {
            create: jest
              .fn()
              .mockResolvedValue(PaymentFixtures.getMockStripeSession()),
            retrieve: jest
              .fn()
              .mockResolvedValue(PaymentFixtures.getMockStripeSession()),
          },
        },
        webhooks: {
          constructEvent: jest
            .fn()
            .mockReturnValue(PaymentFixtures.getMockStripeWebhookEvent()),
        },
      })
      .overrideProvider('PrismaService')
      .useValue({
        payment: {
          create: jest
            .fn()
            .mockResolvedValue(PaymentFixtures.getMockUserPayment()),
          findUnique: jest
            .fn()
            .mockResolvedValue(PaymentFixtures.getMockUserPayment()),
          findMany: jest
            .fn()
            .mockResolvedValue([PaymentFixtures.getMockUserPayment()]),
          update: jest.fn().mockResolvedValue({
            ...PaymentFixtures.getMockUserPayment(),
            status: 'completed',
          }),
          aggregate: jest.fn().mockResolvedValue({
            _sum: { amount: 100000 },
            _count: 10,
          }),
          groupBy: jest.fn().mockResolvedValue([
            { payment_type: 'user', _sum: { amount: 80000 }, _count: 8 },
            { payment_type: 'guest', _sum: { amount: 20000 }, _count: 2 },
          ]),
        },
        service: {
          findUnique: jest
            .fn()
            .mockResolvedValue(PaymentFixtures.getMockService()),
        },
        packages: {
          findUnique: jest
            .fn()
            .mockResolvedValue(PaymentFixtures.getMockPackage()),
        },
        immigration_service: {
          findUnique: jest
            .fn()
            .mockResolvedValue(PaymentFixtures.getMockImmigrationService()),
        },
        training: {
          findUnique: jest
            .fn()
            .mockResolvedValue(PaymentFixtures.getMockTraining()),
        },
        user: {
          findUnique: jest.fn().mockResolvedValue({
            id: PaymentFixtures.TEST_USER_ID,
            name: 'Test User',
            email: '<EMAIL>',
          }),
        },
      })
      .overrideProvider('MailerService')
      .useValue({
        sendEmail: jest.fn().mockResolvedValue(true),
      })
      .overrideProvider('JwtService')
      .useValue({
        sign: jest.fn().mockReturnValue('mock-jwt-token'),
        verify: jest.fn().mockReturnValue({
          id: PaymentFixtures.TEST_USER_ID,
          email: '<EMAIL>',
          name: 'Test User',
        }),
      })
      .compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    jwtService = moduleFixture.get<JwtService>(JwtService);

    // Generate test token
    authToken = jwtService.sign({
      id: PaymentFixtures.TEST_USER_ID,
      email: '<EMAIL>',
      name: 'Test User',
    });
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /v2/payment/create', () => {
    it('should create user service payment successfully', async () => {
      // Arrange
      const paymentDto = PaymentFixtures.getValidUserServicePayment();

      // Act
      const response = await request(app.getHttpServer())
        .post('/v2/payment/create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(paymentDto)
        .expect(200);

      // Assert
      expect(response.body).toEqual({
        status: 'success',
        url: expect.stringContaining('checkout.stripe.com'),
        paymentId: expect.any(String),
      });
    });

    it('should return 400 for missing required fields', async () => {
      // Arrange
      const invalidDto = PaymentFixtures.getInvalidPaymentMissingServiceId();

      // Act & Assert
      const response = await request(app.getHttpServer())
        .post('/v2/payment/create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidDto)
        .expect(400);

      expect(response.body.message).toBeDefined();
    });

    it('should return 401 for user payment without authentication', async () => {
      // Arrange
      const paymentDto = PaymentFixtures.getValidUserServicePayment();

      // Act & Assert
      await request(app.getHttpServer())
        .post('/v2/payment/create')
        .send(paymentDto)
        .expect(401);
    });

    it('should support different service types', async () => {
      const testCases = [
        {
          dto: PaymentFixtures.getValidUserServicePayment(),
          expectedType: ServiceType.SERVICE,
        },
        {
          dto: {
            serviceType: ServiceType.PACKAGE,
            serviceId: PaymentFixtures.TEST_PACKAGE_ID,
            paymentType: PaymentType.USER,
          },
          expectedType: ServiceType.PACKAGE,
        },
        {
          dto: PaymentFixtures.getValidUserImmigrationPayment(),
          expectedType: ServiceType.IMMIGRATION,
        },
        {
          dto: {
            serviceType: ServiceType.TRAINING,
            serviceId: PaymentFixtures.TEST_TRAINING_ID,
            paymentType: PaymentType.USER,
          },
          expectedType: ServiceType.TRAINING,
        },
      ];

      for (const testCase of testCases) {
        const response = await request(app.getHttpServer())
          .post('/v2/payment/create')
          .set('Authorization', `Bearer ${authToken}`)
          .send(testCase.dto)
          .expect(200);

        expect(response.body.status).toBe('success');
      }
    });
  });

  describe('POST /v2/payment/guest', () => {
    it('should create guest payment successfully', async () => {
      // Arrange
      const guestDto = PaymentFixtures.getValidGuestPackagePayment();

      // Act
      const response = await request(app.getHttpServer())
        .post('/v2/payment/guest')
        .send(guestDto)
        .expect(200);

      // Assert
      expect(response.body).toEqual({
        status: 'success',
        url: expect.stringContaining('checkout.stripe.com'),
        paymentId: expect.any(String),
      });
    });

    it('should return 400 for missing guest information', async () => {
      // Arrange
      const incompleteDto = PaymentFixtures.getInvalidGuestPaymentMissingInfo();

      // Act & Assert
      const response = await request(app.getHttpServer())
        .post('/v2/payment/guest')
        .send(incompleteDto)
        .expect(400);

      expect(response.body.message).toBeDefined();
    });
  });

  describe('POST /v2/payment/webhook', () => {
    it('should process webhook successfully', async () => {
      // Arrange
      const webhookEvent = PaymentFixtures.getMockStripeWebhookEvent();

      // Act
      const response = await request(app.getHttpServer())
        .post('/v2/payment/webhook')
        .set('stripe-signature', 'test-signature')
        .send(webhookEvent)
        .expect(200);

      // Assert
      expect(response.body).toEqual({ received: true });
    });
  });

  describe('GET /v2/payment/analytics', () => {
    it('should return payment analytics', async () => {
      // Act
      const response = await request(app.getHttpServer())
        .get('/v2/payment/analytics')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Assert
      expect(response.body).toEqual({
        totalRevenue: expect.any(Number),
        totalPayments: expect.any(Number),
        paymentsByType: expect.any(Array),
        paymentsByService: expect.any(Array),
        recentPayments: expect.any(Array),
      });
    });
  });

  describe('GET /v2/payment/health', () => {
    it('should return service health status', async () => {
      // Act
      const response = await request(app.getHttpServer())
        .get('/v2/payment/health')
        .expect(200);

      // Assert
      expect(response.body).toEqual({
        status: 'OK',
        service: 'unified-payment-service',
        version: expect.any(String),
        timestamp: expect.any(String),
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid JSON in request body', async () => {
      // Act & Assert
      await request(app.getHttpServer())
        .post('/v2/payment/create')
        .set('Authorization', `Bearer ${authToken}`)
        .set('Content-Type', 'application/json')
        .send('invalid json')
        .expect(400);
    });

    it('should handle missing content-type header', async () => {
      // Arrange
      const paymentDto = PaymentFixtures.getValidUserServicePayment();

      // Act & Assert
      await request(app.getHttpServer())
        .post('/v2/payment/create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(paymentDto)
        .expect(200); // Should still work with default content-type
    });

    it('should handle malformed authorization header', async () => {
      // Arrange
      const paymentDto = PaymentFixtures.getValidUserServicePayment();

      // Act & Assert
      await request(app.getHttpServer())
        .post('/v2/payment/create')
        .set('Authorization', 'InvalidToken')
        .send(paymentDto)
        .expect(401);
    });
  });
});
