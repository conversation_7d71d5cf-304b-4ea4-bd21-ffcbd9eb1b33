/**
 * Global Test Setup
 * 
 * This file configures the global testing environment for the Career Ireland API.
 * It sets up common mocks, test utilities, and environment variables needed
 * across all test suites.
 * 
 * Features:
 * - Environment variable configuration
 * - Global mocks for external services
 * - Test database setup
 * - Common test utilities
 * - Performance monitoring
 */

import { config } from 'dotenv';

// Load test environment variables
config({ path: '.env.test' });

// Set default test environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';
process.env.JWT_REFRESH_SECRET = 'test-refresh-secret-key-for-testing-only';
process.env.STRIPE_SECRET_KEY = 'sk_test_fake_stripe_key_for_testing';
process.env.STRIPE_WEBHOOK_SECRET = 'whsec_test_fake_webhook_secret';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/careerireland_test';
process.env.SUPABASE_URL = 'https://fake-supabase-url.supabase.co';
process.env.SUPABASE_ANON_KEY = 'fake-supabase-anon-key-for-testing';
process.env.OPENAI_API_KEY = 'sk-fake-openai-key-for-testing';
process.env.RESEND_API_KEY = 'fake-resend-api-key-for-testing';

// Global test timeout
jest.setTimeout(30000);

// Mock external services globally
jest.mock('stripe', () => ({
  __esModule: true,
  default: jest.fn().mockImplementation(() => ({
    checkout: {
      sessions: {
        create: jest.fn(),
        retrieve: jest.fn(),
      },
    },
    webhooks: {
      constructEvent: jest.fn(),
    },
  })),
}));

jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    storage: {
      from: jest.fn(() => ({
        upload: jest.fn(),
        download: jest.fn(),
        remove: jest.fn(),
        getPublicUrl: jest.fn(),
      })),
    },
  })),
}));

jest.mock('openai', () => ({
  OpenAI: jest.fn().mockImplementation(() => ({
    chat: {
      completions: {
        create: jest.fn(),
      },
    },
  })),
}));

jest.mock('resend', () => ({
  Resend: jest.fn().mockImplementation(() => ({
    emails: {
      send: jest.fn(),
    },
  })),
}));

// Global test utilities
global.testUtils = {
  // Generate test user data
  createTestUser: (overrides = {}) => ({
    id: 'test-user-id',
    name: 'Test User',
    email: '<EMAIL>',
    emailVerified: true,
    provider: 'credentials',
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  }),

  // Generate test mentor data
  createTestMentor: (overrides = {}) => ({
    id: 'test-mentor-id',
    name: 'Test Mentor',
    email: '<EMAIL>',
    emailVerified: true,
    designation: 'Senior Developer',
    desc: 'Experienced software developer',
    location: 'Dublin, Ireland',
    status: 'Active',
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  }),

  // Generate test payment data
  createTestPayment: (overrides = {}) => ({
    id: 'test-payment-id',
    serviceType: 'service',
    serviceId: 'test-service-id',
    paymentType: 'user',
    userId: 'test-user-id',
    amount: 10000, // $100.00 in cents
    currency: 'eur',
    status: 'completed',
    stripeSessionId: 'cs_test_session_id',
    stripePaymentIntentId: 'pi_test_payment_intent',
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  }),

  // Generate test service data
  createTestService: (overrides = {}) => ({
    id: 'test-service-id',
    name: 'Test Service',
    amount: 10000,
    desc: 'Test service description',
    mentorId: 'test-mentor-id',
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  }),

  // Generate JWT payload
  createJWTPayload: (overrides = {}) => ({
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Test User',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 3600,
    ...overrides,
  }),

  // Wait for async operations
  wait: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

  // Clean up test data
  cleanup: async () => {
    // This would typically clean up test database records
    // Implementation depends on your test database strategy
  },
};

// Console override for cleaner test output
const originalConsole = console;
global.console = {
  ...originalConsole,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: originalConsole.error, // Keep errors visible
};

// Restore console after tests
afterAll(() => {
  global.console = originalConsole;
});
